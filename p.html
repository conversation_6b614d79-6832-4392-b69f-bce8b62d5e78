<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>مواقيت الصلاة</title>
  <link
    href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;600;700&family=Reem+Kufi:wght@400;600;700&display=swap"
    rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --gold-primary: #DAA520;
      --gold-secondary: #FFD700;
      --gold-tertiary: #F0E68C;
      --brown-primary: #8B4513;
      --brown-secondary: #A0522D;
      --brown-tertiary: #CD853F;
      --green-primary: #2C5530;
      --green-secondary: #3D8B40;
      --blue-primary: #1a3a5f;
      --white-primary: #FFFDF7;
      --white-secondary: #F8F4E9;
      --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
      --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.15);
      --shadow-heavy: 0 12px 40px rgba(0, 0, 0, 0.2);
      --prayer-point-size: 60px;
      --atmosphere-opacity: 0.6;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, #f8f4e9 0%, #e9e1d0 100%);
      min-height: 100vh;
      font-family: 'Amiri', serif;
      direction: rtl;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      color: #333;
    }

    .container {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
      padding: 2rem;
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      background-color: var(--white-primary);
      border-radius: 20px;
      box-shadow: var(--shadow-medium);
    }

    .content-box {
      padding: 2rem;
      background-color: var(--white-secondary);
      border-radius: 15px;
      box-shadow: var(--shadow-medium);
      position: relative;
      overflow: hidden;
      transition: opacity 0.3s ease;
    }

    .content-box.loading {
      opacity: 0.7;
    }

    .content-box:not(.active) {
      display: none;
    }

    .verse-text,
    .hadith-text {
      font-size: 1.3rem;
      line-height: 1.8;
      margin-bottom: 1rem;
      color: var(--white-primary);
      text-align: justify;
    }

    .verse-source,
    .hadith-source {
      color: var(--gold-primary);
      font-size: 1.1rem;
      text-align: left;
    }

    .main-card {
      background: var(--white-primary);
      border-radius: 24px;
      box-shadow: var(--shadow-heavy);
      overflow: hidden;
      border: 1px solid rgba(139, 69, 19, 0.1);
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto 1fr;
      position: relative;
    }

    .main-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, var(--brown-primary), var(--gold-primary), var(--brown-primary));
      border-radius: 24px 24px 0 0;
    }

    .islamic-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.08;
      background-image:
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%238b4513' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
      pointer-events: none;
    }

    .header-section {
      grid-column: 1 / -1;
      background: linear-gradient(135deg, var(--white-primary) 0%, var(--white-secondary) 100%);
      padding: 0;
      display: grid;
      grid-template-columns: 0.75fr 1fr 0.75fr;
      align-items: center;
      border-bottom: 1px solid rgba(139, 69, 19, 0.1);
      gap: 30px;
    }

    .current-prayer-status {
      text-align: right;
      position: relative;
      padding: 25px;
      background: linear-gradient(135deg, var(--brown-primary) 0%, var(--brown-secondary) 100%);
      color: white;
      box-shadow: var(--shadow-heavy);
      transform: perspective(1000px) rotateX(2deg);
      transition: all 0.4s ease;
      z-index: 2;
      overflow: hidden;
    }

    .current-prayer-status:hover {
      box-shadow: 0 15px 35px rgba(139, 69, 19, 0.4);
    }

    /* Enhanced Header State Transitions with Particle Effects */
    .current-prayer-status {
      position: relative;
      overflow: hidden;
    }

    .current-prayer-status.fade-transition {
      opacity: 0;
      transform: perspective(1000px) rotateX(15deg) scale(0.8);
      filter: blur(8px);
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Background particle field for header */
    .header-particle-field {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      opacity: 0.3;
      pointer-events: none;
    }

    .header-bg-particle {
      position: absolute;
      background: radial-gradient(circle, rgba(218, 165, 32, 0.6), transparent);
      border-radius: 50%;
      animation: floatHeaderParticle 6s infinite linear;
    }

    /* Dissolve particles for state transitions */
    .header-dissolve-particle {
      position: absolute;
      border-radius: 50%;
      z-index: 10;
      opacity: 0;
      pointer-events: none;
      box-shadow: 0 0 10px currentColor;
    }

    .particle-gold {
      background: radial-gradient(circle, #FFD700, #DAA520, #B8860B);
      animation-duration: 2.5s;
      box-shadow: 0 0 15px #FFD700, 0 0 30px rgba(255, 215, 0, 0.5);
    }

    .particle-red {
      background: radial-gradient(circle, #FF4444, #FF1744, #D32F2F);
      animation-duration: 2s;
      box-shadow: 0 0 15px #FF4444, 0 0 30px rgba(255, 68, 68, 0.6);
    }

    .particle-green {
      background: radial-gradient(circle, #4CAF50, #2E7D32, #1B5E20);
      animation-duration: 3s;
      box-shadow: 0 0 15px #4CAF50, 0 0 30px rgba(76, 175, 80, 0.5);
    }

    .current-prayer-status.transitioning .header-dissolve-particle {
      animation-name: headerDissolveParticle;
      animation-fill-mode: forwards;
      animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Shockwave effect for state transitions */
    .header-shockwave {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border: 2px solid rgba(218, 165, 32, 0.6);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      z-index: 5;
      opacity: 0;
    }

    .current-prayer-status.transitioning .header-shockwave {
      animation: headerShockwaveExpand 1.5s forwards;
    }

    /* Enhanced Iqama State Styles */
    .current-prayer-status.iqama-state {
      background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
      animation: iqamaPulse 2s ease-in-out infinite;
      box-shadow:
        var(--shadow-heavy),
        0 0 30px rgba(244, 67, 54, 0.5),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
    }

    @keyframes iqamaPulse {

      0%,
      100% {
        box-shadow:
          var(--shadow-heavy),
          0 0 0 0 rgba(244, 67, 54, 0.7),
          0 0 30px rgba(244, 67, 54, 0.5),
          inset 0 0 30px rgba(255, 255, 255, 0.05);
      }

      50% {
        box-shadow:
          var(--shadow-heavy),
          0 0 0 15px rgba(244, 67, 54, 0),
          0 0 50px rgba(244, 67, 54, 0.8),
          inset 0 0 30px rgba(255, 255, 255, 0.1);
      }
    }

    /* Enhanced Prayer State Styles */
    .current-prayer-status.prayer-state {
      background: linear-gradient(135deg, var(--green-primary) 0%, var(--green-secondary) 100%);
      box-shadow:
        var(--shadow-heavy),
        0 0 30px rgba(61, 139, 64, 0.5),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
    }

    .prayer-hands-icon {
      font-size: 28px;
      margin-left: 10px;
      animation: prayerGlow 3s ease-in-out infinite;
      text-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
    }

    @keyframes prayerGlow {

      0%,
      100% {
        opacity: 0.8;
        transform: scale(1);
        text-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
      }

      50% {
        opacity: 1;
        transform: scale(1.1);
        text-shadow: 0 0 25px rgba(255, 255, 255, 1);
      }
    }

    /* Enhanced keyframe animations */
    @keyframes floatHeaderParticle {
      0% {
        transform: translateY(100%) translateX(0) scale(0);
        opacity: 0;
      }

      10% {
        opacity: 1;
      }

      90% {
        opacity: 1;
      }

      100% {
        transform: translateY(-20px) translateX(30px) scale(1);
        opacity: 0;
      }
    }

    @keyframes headerDissolveParticle {
      0% {
        opacity: 1;
        transform: translate(0, 0) scale(1) rotate(0deg);
        filter: blur(0px);
      }

      50% {
        opacity: 0.8;
        filter: blur(2px);
      }

      100% {
        opacity: 0;
        transform: translate(var(--tx), var(--ty)) scale(0.1) rotate(360deg);
        filter: blur(8px);
      }
    }

    @keyframes headerShockwaveExpand {
      0% {
        width: 0;
        height: 0;
        opacity: 0.9;
        border-width: 3px;
      }

      30% {
        opacity: 0.7;
        border-width: 2px;
      }

      70% {
        opacity: 0.3;
        border-width: 1px;
      }

      100% {
        width: 500px;
        /* Bigger shockwave for more impact */
        height: 500px;
        opacity: 0;
        border-width: 0px;
      }
    }

    @keyframes headerCrackEffect {
      0% {
        opacity: 0.9;
        transform: scale(0.2);
        filter: blur(0px);
      }

      20% {
        opacity: 1;
        transform: scale(0.8);
        filter: blur(1px);
      }

      50% {
        opacity: 0.8;
        transform: scale(1.2);
        filter: blur(2px);
      }

      80% {
        opacity: 0.4;
        transform: scale(2);
        filter: blur(4px);
      }

      100% {
        opacity: 0;
        transform: scale(3.5);
        filter: blur(8px);
      }
    }

    @keyframes lightningFlash {
      0% {
        opacity: 0;
        transform: scale(1) rotate(var(--rotation, 0deg));
      }

      10% {
        opacity: 1;
        transform: scale(1.2) rotate(var(--rotation, 0deg));
      }

      30% {
        opacity: 0.8;
        transform: scale(1) rotate(var(--rotation, 0deg));
      }

      50% {
        opacity: 1;
        transform: scale(1.1) rotate(var(--rotation, 0deg));
      }

      100% {
        opacity: 0;
        transform: scale(0.8) rotate(var(--rotation, 0deg));
      }
    }

    /* INTENSE Screen shake effect for maximum impact */
    @keyframes screenShake {
      0% {
        transform: translate(0, 0) rotate(0deg);
      }

      10% {
        transform: translate(-2px, -1px) rotate(-0.5deg);
      }

      20% {
        transform: translate(-1px, 2px) rotate(0.5deg);
      }

      30% {
        transform: translate(2px, 1px) rotate(-0.5deg);
      }

      40% {
        transform: translate(1px, -2px) rotate(0.5deg);
      }

      50% {
        transform: translate(-1px, 1px) rotate(-0.5deg);
      }

      60% {
        transform: translate(2px, -1px) rotate(0.5deg);
      }

      70% {
        transform: translate(-2px, 2px) rotate(-0.5deg);
      }

      80% {
        transform: translate(1px, 1px) rotate(0.5deg);
      }

      90% {
        transform: translate(-1px, -2px) rotate(-0.5deg);
      }

      100% {
        transform: translate(0, 0) rotate(0deg);
      }
    }

    /* Dramatic full-screen flash effect */
    @keyframes dramaticFlash {
      0% {
        opacity: 0;
        transform: scale(0.8);
      }

      15% {
        opacity: 1;
        transform: scale(1.1);
      }

      30% {
        opacity: 0.7;
        transform: scale(1);
      }

      50% {
        opacity: 0.9;
        transform: scale(1.05);
      }

      100% {
        opacity: 0;
        transform: scale(1);
      }
    }

    /* Enhanced state-specific styling */
    .current-prayer-status.iqama-state .current-prayer-name {
      font-size: 32px;
      font-weight: 800;
      text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
      animation: iqamaTextGlow 2s ease-in-out infinite alternate;
    }

    .current-prayer-status.iqama-state .current-prayer-time {
      font-size: 24px;
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    }

    @keyframes iqamaTextGlow {
      from {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
        transform: scale(1);
      }

      to {
        text-shadow: 0 0 30px rgba(255, 255, 255, 1);
        transform: scale(1.02);
      }
    }

    /* Prayer state specific styling */
    .current-prayer-status.prayer-state .current-prayer-name {
      font-size: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      text-shadow: 0 0 25px rgba(255, 255, 255, 0.9);
      animation: prayerTextGlow 3s ease-in-out infinite;
    }

    .current-prayer-status.prayer-state .current-prayer-time {
      font-size: 22px;
      color: rgba(255, 255, 255, 0.95);
      font-style: italic;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    /* Beautiful Shahada styling for prayer state */
    .current-prayer-status.prayer-state .time-remaining {
      color: rgba(255, 255, 255, 0.98);
      font-size: 16px;
      line-height: 1.5;
      font-weight: 500;
      letter-spacing: 0.8px;
      text-shadow:
        0 0 15px rgba(255, 255, 255, 0.8),
        0 2px 8px rgba(0, 0, 0, 0.3);
      animation: shahadaGlow 4s ease-in-out infinite alternate;
      text-align: center;
      padding: 8px 12px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(5px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    @keyframes prayerTextGlow {

      0%,
      100% {
        text-shadow: 0 0 25px rgba(255, 255, 255, 0.9);
        transform: scale(1);
      }

      50% {
        text-shadow: 0 0 35px rgba(255, 255, 255, 1);
        transform: scale(1.01);
      }
    }

    @keyframes shahadaGlow {

      0%,
      100% {
        text-shadow:
          0 0 15px rgba(255, 255, 255, 0.8),
          0 2px 8px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.05);
      }

      50% {
        text-shadow:
          0 0 25px rgba(255, 255, 255, 1),
          0 0 40px rgba(76, 175, 80, 0.6),
          0 2px 8px rgba(0, 0, 0, 0.3);
        background: rgba(255, 255, 255, 0.08);
      }
    }

    .current-prayer-label {
      font-size: 18px;
      font-weight: 600;
      color: var(--gold-secondary);
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .current-prayer-name {
      font-size: 36px;
      font-weight: 700;
      color: white;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      letter-spacing: 1px;
    }

    .current-prayer-time {
      font-size: 32px;
      font-weight: 700;
      color: var(--gold-secondary);
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .time-remaining {
      font-size: 21px;
      color: white;
      font-weight: 600;
      background: rgba(255, 215, 0, 0.2);
      padding: 12px 20px;
      border-radius: 30px;
      display: inline-flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      border: 1px solid rgba(255, 215, 0, 0.3);
    }

    .location-name {
      font-size: 18px;
      color: var(--gold-tertiary);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .header-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      margin: 2rem 0;
    }

    /* .main-title {
      font-size: 48px;
      font-weight: 700;
      color: var(--brown-primary);
      text-align: center;
      font-family: 'Reem Kufi', sans-serif;
      margin: 0;
      padding: 0 40px;
      position: relative;
      display: inline-block;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      letter-spacing: 1px;
    }

    .main-title::before,
    .main-title::after {
      content: "✧";
      color: var(--gold-primary);
      font-size: 36px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      animation: glow 3s ease-in-out infinite alternate;
    }

    .main-title::before {
      right: -25px;
    }

    .main-title::after {
      left: -25px;
    } */

    @keyframes glow {
      from {
        opacity: 0.6;
        text-shadow: 0 0 5px var(--gold-secondary);
      }

      to {
        opacity: 1;
        text-shadow: 0 0 15px var(--gold-secondary);
      }
    }

    .dates-section {
      text-align: left;
      padding: 25px;
      background: linear-gradient(135deg, var(--white-secondary) 0%, var(--white-primary) 100%);
      border-radius: 18px;
      border: 1px solid rgba(139, 69, 19, 0.1);
      box-shadow: var(--shadow-light);
      z-index: 2;
    }

    .date-row {
      margin-bottom: 18px;
      position: relative;
    }

    .date-label {
      font-size: 18px;
      color: var(--brown-primary);
      font-weight: 600;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .date-value {
      font-size: 24px;
      font-weight: 700;
      color: var(--green-primary);
      display: flex;
      align-items: center;
      gap: 15px;
    }

    /* Dynamic CSS Moon */
    .moon-container {
      position: relative;
      width: 36px;
      height: 36px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      vertical-align: baseline;
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 0 12px rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;
    }

    .moon-canvas {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: block;
      background: transparent;
    }

    /* Enhanced hover effects */
    .moon-container:hover {
      transform: scale(1.05);
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
    }

    .moon-container:hover .moon-canvas {
      filter: brightness(1.1);
    }

    /* Lunar features for realistic moon rendering */
    .moon-surface {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      pointer-events: none;
      background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.4) 0%, transparent 30%),
        radial-gradient(circle at 75% 35%, rgba(0, 0, 0, 0.08) 0%, transparent 25%),
        radial-gradient(circle at 45% 65%, rgba(0, 0, 0, 0.06) 0%, transparent 20%),
        radial-gradient(circle at 65% 75%, rgba(255, 255, 255, 0.2) 0%, transparent 15%);
    }

    /* Base moon styling */
    .moon-base {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg,
          #e8e8e8 0%,
          #d0d0d0 30%,
          #b8b8b8 70%,
          #a0a0a0 100%);
      box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.25),
        0 1px 4px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    }



    .current-time {
      font-size: 23px;
      color: #555;
      font-weight: 600;
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background: rgba(139, 69, 19, 0.05);
      border-radius: 10px;
    }

    /* Main Content Area */
    .content-section {
      grid-column: 1 / -1;
      display: grid;
      grid-template-columns: 2fr 1fr;
      height: 100%;
    }

    /* Prayer Timeline Section */
    .prayer-timeline-section {
      padding: 50px 45px 35px 45px;
      display: flex;
      flex-direction: column;
      background: linear-gradient(135deg, var(--white-secondary) 0%, var(--white-primary) 100%);
      border-right: 1px solid rgba(139, 69, 19, 0.1);
      position: relative;
      overflow: visible;
      margin: 20px;
    }

    /* Decorative Border Styles */
    .decorative-border-container {
      position: absolute;
      top: -15px;
      left: -15px;
      right: -15px;
      bottom: -15px;
      pointer-events: none;
      z-index: 1;
      border: 2px solid rgba(139, 69, 19, 0.3);
      border-radius: 15px;
    }

    .decorative-border {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      overflow: visible;
    }

    .decorative-border.top {
      top: 0;
      left: 80px;
      width: calc(100% - 160px);
      height: 80px;
      flex-direction: row;
    }

    .decorative-border.right {
      top: 80px;
      right: 0;
      width: 80px;
      height: calc(100% - 160px);
      flex-direction: column;
    }

    .decorative-border.bottom {
      bottom: 0;
      left: 80px;
      width: calc(100% - 160px);
      height: 80px;
      flex-direction: row;
    }

    .decorative-border.left {
      top: 80px;
      left: 0;
      width: 80px;
      height: calc(100% - 160px);
      flex-direction: column;
    }

    .decorative-border svg {
      display: block;
      flex-shrink: 0;
      shape-rendering: crispEdges;
      width: 40px;
      height: 40px;
      opacity: 0.8;
      margin-left: -1%;
    }

    .decorative-border.right svg {
      transform: rotate(90deg);
      margin-bottom: -8%;
      margin-left: 0;
    }

    .decorative-border.bottom svg {
      transform: rotate(180deg);
    }

    .decorative-border.left svg {
      transform: rotate(270deg);
      margin-bottom: -8%;
      margin-left: 0;
    }

    /* Corner decorations */
    .corner-decoration {
      position: absolute;
      width: 80px;
      height: 80px;
      z-index: 15;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .corner-decoration.top-left {
      top: 0;
      left: 10px;
    }

    .corner-decoration.top-right {
      top: 0;
      right: 10px;
    }

    .corner-decoration.bottom-left {
      bottom: 0;
      left: 10px;
    }

    .corner-decoration.bottom-right {
      bottom: 0;
      right: 10px;
    }

    .corner-decoration svg {
      display: block;
      width: 74px;
      height: 75px;
      shape-rendering: crispEdges;
      opacity: 0.9;
    }

    .section-title {
      font-size: clamp(32px, 6vh, 52px);
      font-weight: 600;
      color: var(--brown-primary);
      text-align: center;
      position: relative;
      padding: clamp(0.8rem, 1.5vh, 1.5rem) 0;
      margin-bottom: clamp(1.5rem, 3vh, 3rem);
      z-index: 2;
      font-family: 'Reem Kufi', sans-serif;
      text-shadow: 0 2px 6px rgba(139, 69, 19, 0.15);
      opacity: 0.95;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 20%;
      left: 20%;
      height: 4px;
      background: linear-gradient(90deg, transparent, var(--gold-primary), transparent);
      border-radius: 2px;
    }

    /* Enhanced Circular Timeline */
    .timeline-container {
      position: relative;
      width: 100%;
      aspect-ratio: 1.5 / 1;
      min-width: 0;
      min-height: 0;
      box-sizing: border-box;
      z-index: 2;
    }

    #prayer-timeline-svg {
      width: 100%;
      max-width: 500px;
      display: block;
      margin: auto;
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .timeline-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;
      /* More responsive width */
      max-width: 400px;
      height: 90%;
      max-height: 400px;
      border-radius: 50%;
      border: 3px solid var(--brown-primary);
      z-index: 1;
    }

    .timeline-path {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 420px;
      height: 420px;
      border-radius: 50%;
      border: 2px dashed var(--brown-tertiary);
      opacity: 0.5;
    }

    .timeline-path,
    #prayer-timeline-svg {
      width: 100%;
      height: 100%;
      max-width: 500px;
      aspect-ratio: 1 / 1;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-sizing: border-box;
    }

    .prayer-point {
      position: absolute;
      width: var(--prayer-point-size);
      height: var(--prayer-point-size);
      box-sizing: border-box;
      /* Remove transform: translate(-50%, -50%) */
      display: flex;
      align-items: center;
      justify-content: center;
      pointer-events: auto;
      margin: 0;
      padding: 0;
      border: none;
    }

    .prayer-point-inner {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.75) 60%, var(--gold-tertiary) 100%),
        linear-gradient(120deg, rgba(255, 255, 255, 0.18) 0%, rgba(218, 165, 32, 0.10) 100%);
      backdrop-filter: blur(6px) saturate(1.3);
      -webkit-backdrop-filter: blur(6px) saturate(1.3);
      border: 2.5px solid var(--gold-primary);
      box-shadow:
        0 0 0 4px rgba(218, 165, 32, 0.10),
        0 2px 16px 0 rgba(218, 165, 32, 0.13),
        0 1.5px 8px 0 rgba(139, 69, 19, 0.10),
        0 0 0 1.5px rgba(255, 255, 255, 0.18) inset,
        0 2px 8px 0 rgba(139, 69, 19, 0.10) inset;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(0 0 12px rgba(218, 165, 32, 0.13));
      transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.25s;
      font-smooth: always;
      -webkit-font-smoothing: antialiased;
    }

    .prayer-point:hover .prayer-point-inner {
      transform: scale(1.15) rotateZ(2deg);
      box-shadow:
        0 0 0 10px rgba(218, 165, 32, 0.18),
        0 8px 32px rgba(218, 165, 32, 0.22),
        0 0 24px 0 rgba(255, 255, 255, 0.18) inset;
      filter: drop-shadow(0 0 24px rgba(218, 165, 32, 0.22));
      z-index: 12;
    }

    .prayer-point.current .prayer-point-inner {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.92) 40%, var(--gold-secondary) 100%),
        linear-gradient(120deg, rgba(255, 255, 255, 0.22) 0%, rgba(218, 165, 32, 0.18) 100%);
      border: 2.5px solid var(--gold-secondary);
      box-shadow:
        0 0 0 16px rgba(218, 165, 32, 0.22),
        0 0 0 8px rgba(139, 69, 19, 0.13),
        0 0 0 2.5px rgba(255, 255, 255, 0.22) inset,
        0 2px 12px 0 rgba(218, 165, 32, 0.18) inset;
      transform: scale(1.22);
      z-index: 14;
    }

    .prayer-point.sunrise .prayer-point-inner {
      background: radial-gradient(circle at 60% 40%, #fffbe6 60%, #ffeaa7 100%),
        linear-gradient(135deg, #fffbe6 60%, #ffeaa7 100%);
      border: 2.5px solid var(--gold-primary);
      box-shadow:
        0 0 0 14px rgba(255, 215, 0, 0.18),
        0 0 0 4px rgba(255, 215, 0, 0.13),
        0 2px 12px 0 rgba(255, 215, 0, 0.10),
        0 0 0 2.5px rgba(255, 255, 255, 0.18) inset;
      filter: drop-shadow(0 0 24px rgba(255, 215, 0, 0.22));
      opacity: 0.99;
    }

    .prayer-name,
    .prayer-time {
      text-shadow: 0 1px 4px rgba(218, 165, 32, 0.10), 0 0.5px 0.5px #fff;
      font-family: 'Reem Kufi', 'Amiri', serif;
    }

    .prayer-point-inner .prayer-icon {
      color: var(--gold-primary);
      text-shadow: 0 1px 8px rgba(218, 165, 32, 0.18), 0 0.5px 0.5px #fff;
      font-size: 24px;
      margin-bottom: 5px;
    }

    .mosque-anchor {
	    position: absolute;
	    top: 47%;
	    left: 50%;
	    transform: translate(-50%, -50%);
	    z-index: 5;
	    opacity: 0.17;
	    transition: opacity 0.4s ease;
	  }
	
	  .mosque-anchor .logo-image {
	    width: 150px;
	    height: 150px;
	    filter: grayscale(50%) brightness(1.1);
	  }

    .mosque-anchor.logo-image:hover {
      transform: scale(1.05);
      filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.25));
    }

    /* Progress indicator container */
    .progress-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      z-index: 5;
      pointer-events: none;
      box-sizing: border-box;
    }

    .progress-indicator {
      position: absolute;
      width: 30px;
      height: 30px;
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .celestial-body {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      position: relative;
      transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      filter: drop-shadow(0 0 0 transparent);
    }

    /* Enhanced Atmosphere Layer */
    .celestial-body::after {
      content: '';
      position: absolute;
      top: -20%;
      left: -20%;
      width: 140%;
      height: 140%;
      border-radius: 50%;
      opacity: 0;
      transition: all 1.5s ease;
      pointer-events: none;
      z-index: -1;
    }

    /* Enhanced Sun Rays */
    .celestial-body::before {
      content: '';
      position: absolute;
      top: -80%;
      left: -80%;
      width: 260%;
      height: 260%;
      background: conic-gradient(transparent 0deg,
          rgba(255, 255, 255, 0.4) 2deg,
          transparent 4deg,
          rgba(255, 255, 255, 0.6) 6deg,
          transparent 8deg,
          rgba(255, 255, 255, 0.3) 10deg,
          transparent 12deg,
          rgba(255, 255, 255, 0.7) 14deg,
          transparent 16deg,
          rgba(255, 255, 255, 0.4) 18deg,
          transparent 20deg,
          rgba(255, 255, 255, 0.5) 22deg,
          transparent 24deg,
          rgba(255, 255, 255, 0.8) 26deg,
          transparent 28deg,
          rgba(255, 255, 255, 0.3) 30deg,
          transparent 32deg,
          rgba(255, 255, 255, 0.6) 34deg,
          transparent 36deg);
      animation: rotateSunRays 20s linear infinite;
      opacity: 0;
      transition: opacity 1.5s ease;
      border-radius: 50%;
      filter: blur(1px);
    }

    /* Corona Effect */
    .day-sun::after,
    .sunrise-sun::after,
    .sunset-sun::after {
      background: radial-gradient(circle at center,
          rgba(255, 255, 255, 0.2) 0%,
          rgba(255, 200, 100, 0.15) 20%,
          rgba(255, 150, 50, 0.1) 40%,
          transparent 60%);
      opacity: var(--atmosphere-opacity);
      animation: coronaPulse 4s ease-in-out infinite;
    }

    .day-sun::before,
    .sunrise-sun::before,
    .sunset-sun::before {
      opacity: 1;
    }

    /* Enhanced Night Moon */
    .night-moon {
      background:
        /* Enhanced crater system */
        radial-gradient(circle at 68% 22%, rgba(0, 0, 0, 0.25) 6%, transparent 10%),
        radial-gradient(circle at 42% 73%, rgba(0, 0, 0, 0.18) 4%, transparent 8%),
        radial-gradient(circle at 78% 58%, rgba(0, 0, 0, 0.22) 3%, transparent 7%),
        radial-gradient(circle at 25% 35%, rgba(0, 0, 0, 0.15) 8%, transparent 12%),
        radial-gradient(circle at 85% 28%, rgba(0, 0, 0, 0.12) 5%, transparent 9%),
        radial-gradient(circle at 55% 85%, rgba(0, 0, 0, 0.08) 7%, transparent 11%),
        /* Subtle surface texture */
        radial-gradient(ellipse at 20% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 40%),
        radial-gradient(ellipse at 80% 30%, rgba(255, 255, 255, 0.03) 0%, transparent 30%),
        /* Main surface gradient with more realistic colors */
        radial-gradient(circle at 35% 25%, #e8f4ff 0%, #c8e3ff 20%, #8bb5e8 60%, #2d4a7a 100%);

      box-shadow:
        0 0 40px rgba(91, 147, 255, 0.8),
        0 0 80px rgba(91, 147, 255, 0.4),
        inset -12px -12px 25px rgba(0, 0, 0, 0.4),
        inset 8px 8px 15px rgba(255, 255, 255, 0.1),
        inset -3px -3px 8px rgba(0, 0, 0, 0.2);

      animation: moonGlow 6s ease-in-out infinite;
      filter: drop-shadow(0 0 20px rgba(91, 147, 255, 0.6));
    }

    .night-moon::after {
      background: radial-gradient(circle at center,
          rgba(91, 147, 255, 0.3) 0%,
          rgba(91, 147, 255, 0.15) 40%,
          transparent 70%);
      opacity: 0.8;
      animation: atmosphereShimmer 8s ease-in-out infinite;
    }

    /* Enhanced Predawn Moon */
    .predawn-moon {
      background:
        radial-gradient(circle at 62% 28%, rgba(0, 0, 0, 0.2) 5%, transparent 9%),
        radial-gradient(circle at 38% 68%, rgba(0, 0, 0, 0.15) 6%, transparent 10%),
        radial-gradient(circle at 75% 45%, rgba(0, 0, 0, 0.12) 4%, transparent 8%),
        radial-gradient(circle at 30% 25%, rgba(0, 0, 0, 0.18) 7%, transparent 11%),
        radial-gradient(ellipse at 15% 70%, rgba(255, 255, 255, 0.04) 0%, transparent 35%),
        radial-gradient(circle at 30% 30%, #f0f8ff 0%, #d1e7ff 30%, #7eb6ff 70%, #4a7ccc 100%);

      box-shadow:
        0 0 35px rgba(126, 182, 255, 0.7),
        0 0 70px rgba(126, 182, 255, 0.3),
        inset -10px -10px 20px rgba(0, 0, 0, 0.3),
        inset 6px 6px 12px rgba(255, 255, 255, 0.08);

      animation: moonGlow 5.5s ease-in-out infinite;
      filter: drop-shadow(0 0 15px rgba(126, 182, 255, 0.5));
    }

    /* Enhanced Sunrise Sun */
    .sunrise-sun {
      background:
        /* Solar flare effects */
        conic-gradient(from 45deg at 30% 30%,
          #ffeb3b 0deg, #ff9800 60deg, #ff5722 120deg,
          #e91e63 180deg, #ff5722 240deg, #ff9800 300deg, #ffeb3b 360deg),
        /* Core heat gradient */
        radial-gradient(circle at 25% 25%, #fffde7 0%, #ffeb3b 30%, #ff9800 70%, #d84315 100%);

      box-shadow:
        0 0 50px rgba(255, 152, 0, 1),
        0 0 100px rgba(255, 87, 34, 0.8),
        0 0 150px rgba(255, 193, 7, 0.5),
        0 0 200px rgba(255, 152, 0, 0.3),
        inset 5px 5px 15px rgba(255, 255, 255, 0.3),
        inset -2px -2px 10px rgba(0, 0, 0, 0.1);

      animation: sunPulse 4s ease-in-out infinite;
      filter: drop-shadow(0 0 30px rgba(255, 152, 0, 0.8));
    }


    /* Enhanced Day Sun */
    .day-sun {
      background:
        /* Dynamic plasma effect */
        conic-gradient(from 0deg at 35% 25%,
          #ffffff 0deg, #fffde7 30deg, #ffeb3b 90deg,
          #ffc107 150deg, #ffeb3b 210deg, #fffde7 270deg, #ffffff 360deg),
        radial-gradient(circle at 30% 30%, #ffffff 0%, #fffde7 20%, #ffeb3b 50%, #ffc107 100%);

      box-shadow:
        0 0 60px rgba(255, 235, 59, 1.2),
        0 0 120px rgba(255, 193, 7, 0.9),
        0 0 180px rgba(255, 235, 59, 0.6),
        0 0 240px rgba(255, 193, 7, 0.3),
        inset 8px 8px 20px rgba(255, 255, 255, 0.4),
        inset -3px -3px 12px rgba(255, 193, 7, 0.2);

      animation: sunPulse 3s ease-in-out infinite, solarFlare 12s ease-in-out infinite;
      filter: drop-shadow(0 0 40px rgba(255, 235, 59, 1));
    }

    /* Enhanced Sunset Sun */
    .sunset-sun {
      background:
        conic-gradient(from 90deg at 25% 35%,
          #ff9800 0deg, #e91e63 45deg, #9c27b0 90deg,
          #673ab7 135deg, #9c27b0 180deg, #e91e63 225deg,
          #ff5722 270deg, #ff9800 315deg, #ff9800 360deg),
        radial-gradient(circle at 25% 35%, #ffcc80 0%, #ff9800 25%, #e91e63 60%, #7b1fa2 100%);

      box-shadow:
        0 0 45px rgba(255, 152, 0, 1),
        0 0 90px rgba(233, 30, 99, 0.8),
        0 0 135px rgba(156, 39, 176, 0.6),
        0 0 180px rgba(103, 58, 183, 0.4),
        inset 6px 6px 18px rgba(255, 204, 128, 0.3),
        inset -4px -4px 12px rgba(123, 31, 162, 0.2);

      animation: sunPulse 4.5s ease-in-out infinite;
      filter: drop-shadow(0 0 35px rgba(233, 30, 99, 0.8));
    }

    /* Enhanced Evening Moon */
    .evening-moon {
      background:
        radial-gradient(circle at 58% 32%, rgba(0, 0, 0, 0.15) 5%, transparent 9%),
        radial-gradient(circle at 72% 52%, rgba(0, 0, 0, 0.12) 3%, transparent 7%),
        radial-gradient(circle at 35% 75%, rgba(0, 0, 0, 0.1) 6%, transparent 10%),
        radial-gradient(circle at 25% 30%, rgba(0, 0, 0, 0.08) 8%, transparent 12%),
        radial-gradient(ellipse at 85% 40%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
        radial-gradient(circle at 30% 30%, #f8fcff 0%, #e1f0ff 25%, #a8d0ff 65%, #5a92d9 100%);

      box-shadow:
        0 0 30px rgba(168, 208, 255, 0.6),
        0 0 60px rgba(90, 146, 217, 0.4),
        inset -8px -8px 18px rgba(0, 0, 0, 0.25),
        inset 4px 4px 10px rgba(255, 255, 255, 0.1);

      animation: moonGlow 5s ease-in-out infinite;
      filter: drop-shadow(0 0 18px rgba(168, 208, 255, 0.5));
    }

    /* Enhanced Animations */
    @keyframes sunPulse {

      0%,
      100% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1) saturate(1) drop-shadow(0 0 30px rgba(255, 193, 7, 0.8));
      }

      25% {
        transform: scale(1.05) rotate(1deg);
        filter: brightness(1.1) saturate(1.1) drop-shadow(0 0 40px rgba(255, 193, 7, 1));
      }

      50% {
        transform: scale(1.12) rotate(0deg);
        filter: brightness(1.25) saturate(1.2) drop-shadow(0 0 50px rgba(255, 193, 7, 1.2));
      }

      75% {
        transform: scale(1.08) rotate(-1deg);
        filter: brightness(1.15) saturate(1.15) drop-shadow(0 0 45px rgba(255, 193, 7, 1.1));
      }
    }

    @keyframes moonGlow {

      0%,
      100% {
        opacity: 0.9;
        transform: scale(1) rotate(0deg);
        filter: brightness(1) contrast(1) drop-shadow(0 0 15px rgba(91, 147, 255, 0.6));
      }

      33% {
        opacity: 0.95;
        transform: scale(1.04) rotate(0.5deg);
        filter: brightness(1.05) contrast(1.05) drop-shadow(0 0 20px rgba(91, 147, 255, 0.8));
      }

      66% {
        opacity: 1;
        transform: scale(1.06) rotate(-0.3deg);
        filter: brightness(1.12) contrast(1.1) drop-shadow(0 0 25px rgba(91, 147, 255, 1));
      }
    }

    @keyframes solarFlare {

      0%,
      90%,
      100% {
        background-size: 100% 100%;
      }

      95% {
        background-size: 110% 110%;
      }
    }

    @keyframes coronaPulse {

      0%,
      100% {
        transform: scale(1);
        opacity: var(--atmosphere-opacity);
      }

      50% {
        transform: scale(1.15);
        opacity: calc(var(--atmosphere-opacity) * 1.3);
      }
    }

    @keyframes atmosphereShimmer {

      0%,
      100% {
        opacity: 0.6;
        transform: scale(1);
      }

      25% {
        opacity: 0.8;
        transform: scale(1.08);
      }

      50% {
        opacity: 0.9;
        transform: scale(1.12);
      }

      75% {
        opacity: 0.7;
        transform: scale(1.05);
      }
    }

    @keyframes rotateSunRays {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    /* Interactive hover effects */
    .progress-indicator:hover .celestial-body {
      transform: scale(1.1);
      transition: all 0.3s ease;
    }

    .progress-indicator:hover .day-sun,
    .progress-indicator:hover .sunrise-sun,
    .progress-indicator:hover .sunset-sun {
      filter: brightness(1.2) drop-shadow(0 0 60px rgba(255, 193, 7, 1.5));
    }

    .progress-indicator:hover .night-moon,
    .progress-indicator:hover .predawn-moon,
    .progress-indicator:hover .evening-moon {
      filter: brightness(1.15) drop-shadow(0 0 30px rgba(91, 147, 255, 1));
    }

    /* Visual sequence indicator with 24 segments */
    .prayer-sequence {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 420px;
      height: 420px;
      border-radius: 50%;
      z-index: 0;
    }

    .sequence-path {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background:
        repeating-conic-gradient(transparent 0deg 7.5deg,
          rgba(139, 69, 19, 0.03) 7.5deg 15deg);
      transform: translate(-50%, -50%);
    }

    /* Hadith Section (Right Side) */
    .hadith-section {
      padding: 35px;
      background: linear-gradient(135deg, var(--brown-primary) 0%, var(--brown-secondary) 100%);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      overflow: hidden;
      border-bottom-left-radius: 24px;
    }

    .hadith-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.15) 0%, transparent 30%);
      pointer-events: none;
      z-index: 1;
    }

    .hadith-title {
      font-size: 32px;
      font-weight: 700;
      text-align: center;
      margin-bottom: 30px;
      color: var(--gold-secondary);
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hadith-text {
      font-size: 26px;
      line-height: 1.9;
      text-align: center;
      margin-bottom: 30px;
      font-style: italic;
      position: relative;
      padding: 0 30px;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .fade-out {
      opacity: 0;
      transition: opacity 0.3s ease-out;
    }

    .hadith-section {
      opacity: 1;
      transition: opacity 0.3s ease-in;
    }

    .hadith-text::before,
    .hadith-text::after {
      content: '"';
      font-size: 50px;
      position: absolute;
      top: -20px;
      color: rgba(255, 215, 0, 0.3);
      font-family: serif;
      z-index: 1;
    }

    .hadith-text::before {
      right: 10px;
    }

    .hadith-text::after {
      left: 10px;
      transform: rotate(180deg);
    }

    .hadith-source {
      font-size: 20px;
      text-align: center;
      font-weight: 600;
      color: var(--gold-tertiary);
      position: relative;
      z-index: 2;
      font-style: italic;
    }

    /* Responsive Design */
    @media (max-width: 1100px) {
      .timeline-container {
        height: 400px;
      }

      .timeline-circle {
        width: 350px;
        height: 350px;
      }

      .timeline-path {
        width: 370px;
        height: 370px;
      }

      /* Progress container inherits timeline container dimensions */

      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }

      .prayer-name {
        font-size: 14px;
      }

      .prayer-time {
        font-size: 16px;
      }

      .progress-indicator {
        width: 25px;
        height: 25px;
      }

      .hadith-text {
        font-size: 24px;
      }
    }

    @media (max-width: 900px) {
      .content-section {
        grid-template-columns: 1fr;
      }

      .header-section {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 25px;
        padding: 25px;
      }

      .current-prayer-status {
        transform: none;
        text-align: center;
      }

      .hadith-section {
        border-bottom-left-radius: 0;
      }

      .timeline-container {
        height: 350px;
      }

      .timeline-circle {
        width: 300px;
        height: 300px;
      }

      .timeline-path {
        width: 320px;
        height: 320px;
      }

      /* Progress container inherits timeline container dimensions */

      /* Adjust prayer positions for medium screens */
      .prayer-point.isha {
        top: 25%;
        right: 10%;
      }

      .prayer-point.sunrise {
        top: 75%;
        right: 10%;
        width: 45px;
        height: 45px;
      }

      .prayer-point.sunrise .prayer-name {
        font-size: 12px;
      }

      .prayer-point.sunrise .prayer-time {
        font-size: 14px;
      }

      .prayer-point.asr {
        top: 25%;
        left: 10%;
      }

      @keyframes moveProgress {
        0% {
          top: 100%;
          left: 50%;
        }

        100% {
          top: 25%;
          left: 10%;
        }
      }
    }

    @media (max-width: 600px) {
      .main-title {
        font-size: 36px;
        padding: 0 20px;
        margin: 30px;
      }

      .header-section {
        padding: 20px;
        gap: 20px;
      }

      .hadith-text {
        font-size: 22px;
      }

      .timeline-container {
        height: 300px;
      }

      .timeline-circle {
        width: 250px;
        height: 250px;
      }

      .timeline-path {
        width: 270px;
        height: 270px;
      }

      /* Progress container inherits timeline container dimensions */

      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }

      .prayer-name {
        font-size: 12px;
      }

      .prayer-time {
        font-size: 14px;
      }

      .progress-indicator {
        width: 50px;
        height: 50px;
      }

      .current-prayer-status {
        padding: 15px;
      }

      .current-prayer-name {
        font-size: 28px;
      }

      .current-prayer-time {
        font-size: 24px;
      }

      .dates-section {
        padding: 15px;
      }

      /* Adjust prayer positions for small screens */
      .prayer-point.isha {
        top: 25%;
        right: 5%;
      }

      .prayer-point.sunrise {
        top: 75%;
        right: 5%;
        width: 40px;
        height: 40px;
      }

      .prayer-point.sunrise .prayer-name {
        font-size: 10px;
      }

      .prayer-point.sunrise .prayer-time {
        font-size: 12px;
      }

      .prayer-point.asr {
        top: 25%;
        left: 5%;
      }

      @keyframes moveProgress {
        0% {
          top: 100%;
          left: 50%;
        }

        100% {
          top: 25%;
          left: 5%;
        }
      }
    }

    /* Extra small screens */
    @media (max-width: 480px) {
      .timeline-container {
        height: 280px;
      }

      .timeline-circle {
        width: 220px;
        height: 220px;
      }

      .timeline-path {
        width: 240px;
        height: 240px;
      }

      /* Progress container inherits timeline container dimensions */

      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }

      .prayer-name {
        font-size: 10px;
      }

      .prayer-time {
        font-size: 12px;
      }
    }

    /* Decorative elements */
    .islamic-decoration {
      position: absolute;
      z-index: 1;
      color: rgba(139, 69, 19, 0.1);
      font-size: 40px;
    }

    .decoration-1 {
      top: 15%;
      left: 5%;
      transform: rotate(45deg);
    }

    .decoration-2 {
      bottom: 20%;
      right: 5%;
      transform: rotate(-20deg);
    }

    .decoration-3 {
      top: 40%;
      right: 10%;
      font-size: 30px;
      transform: rotate(15deg);
    }

    .decoration-4 {
      bottom: 30%;
      left: 10%;
      font-size: 30px;
      transform: rotate(-15deg);
    }

    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    @media (max-width: 900px) {
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
    }

    @media (max-width: 600px) {
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
    }

    .prayer-point.sunrise {
      background: linear-gradient(135deg, #fffbe6 60%, #ffeaa7 100%);
      border: 2.5px solid var(--gold-primary);
      box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.13), 0 2px 12px 0 rgba(255, 215, 0, 0.10);
      filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.18));
      opacity: 0.98;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="main-card">
      <div class="islamic-pattern"></div>

      <!-- Islamic decorative elements -->
      <div class="islamic-decoration decoration-1">✧</div>
      <div class="islamic-decoration decoration-2">✧</div>
      <div class="islamic-decoration decoration-3">✧</div>
      <div class="islamic-decoration decoration-4">✧</div>

      <div class="header-section">
        <div class="current-prayer-status">
          <!-- Background particle field -->
          <div class="header-particle-field" id="headerParticleField"></div>

          <!-- Shockwave effect -->
          <div class="header-shockwave" id="headerShockwave"></div>

          <div class="current-prayer-label">
            <i class="fas fa-clock"></i> الصلاة الحالية
          </div>
          <div class="current-prayer-name">الظهر</div>
          <div class="current-prayer-time">١٢:٣٠</div>
          <div class="time-remaining">
            <i class="fas fa-hourglass-half"></i> ٣ ساعات و٤٥ دقيقة للعصر
          </div>
          <div class="location-name">
            <i class="fas fa-location-dot"></i> صيدا، لبنان
          </div>

        </div>

        <div class="header-content">

        </div>

        <div class="dates-section">
          <div class="date-row">
            <div class="date-label">
              <i class="fas fa-calendar-days"></i> التاريخ الهجري
            </div>
            <div class="date-value">
              ١٢ محرم ١٤٤٧
              <span class="moon-phase">
                <i class="fas fa-moon"></i>
              </span>
            </div>
          </div>
          <div class="date-row">
            <div class="date-label">
              <i class="fas fa-calendar"></i> التاريخ الميلادي
            </div>
            <div class="date-value">١٨ يوليو ٢٠٢٥</div>
          </div>
          <div class="current-time">
            <i class="fas fa-calendar-check"></i> الجمعة، ١٢:٣٠ مساءً
          </div>
        </div>
      </div>

      <div class="content-section">
        <div class="prayer-timeline-section">
          <!-- Decorative border containers -->
          <div class="decorative-border-container">
            <div class="decorative-border top"></div>
            <div class="decorative-border right"></div>
            <div class="decorative-border bottom"></div>
            <div class="decorative-border left"></div>

            <!-- Corner decorations -->
            <div class="corner-decoration top-left"></div>
            <div class="corner-decoration top-right"></div>
            <div class="corner-decoration bottom-left"></div>
            <div class="corner-decoration bottom-right"></div>
          </div>

          <h2 class="section-title">مواقيت الصلاة</h2>
          <div class="timeline-container">
            <svg id="prayer-timeline-svg" width="100%" height="100%" viewBox="0 0 1000 1000"
              style="max-width: 500px; display: block; margin: auto;"></svg>

            <div class="mosque-anchor">
			        <div class="mosque-logo">
			          <img src="unnamed1.PNG" alt="شعار المسجد" class="logo-image">
			        </div>
			      </div>

            <div class="progress-container">
              <div class="progress-indicator" id="progressIndicator">
                <div class="celestial-body day-sun" id="celestialBody"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="hadith-section" id="content-section">
          <h3 class="hadith-title" id="content-title">حديث شريف</h3>
          <div class="hadith-text" id="content-text"></div>
          <div class="hadith-source" id="content-source"></div>
        </div>
      </div>
    </div>
  </div>
  <script>
const CONFIG = {
  timezone: 'Asia/Beirut',
  prayerMethod: 5,
  synodicMonth: 29.530588,
  islamicEpochJulian: 1948439.5,
  leapCycle: [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29],
  minutesPerDay: 1440
};

const LOCALIZATION = {
  arabicMonths: [
    'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران',
    'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'
  ],
  prayerNames: {
    Fajr: 'الفجر', Sunrise: 'الشروق', Dhuhr: 'الظهر',
    Asr: 'العصر', Maghrib: 'المغرب', Isha: 'العشاء',
    Firstthird: 'الثلث الثاني', Lastthird: 'الثلث الأخير'
  },
  arabicDigits: ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'],
  weekdays: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
  hijriMonths: [
    'محرم', 'صفر', 'ربيع الأول', 'ربيع الآخر', 'جمادى الأولى', 'جمادى الآخرة',
    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
  ]
};

const MOON_DATA = {
  phases: ['🌒', '🌓', '🌔', '🌕', '🌖', '🌗', '🌘', '🌑'],
  phaseNames: ["New Moon", "Waxing Crescent", "First Quarter", "Waxing Gibbous", "Full Moon", "Waning Gibbous", "Last Quarter", "Waning Crescent"],
  phaseBoundaries: [0.0174, 0.2326, 0.2674, 0.4826, 0.5174, 0.7326, 0.7674, 0.9826]
};

const COLLECTIONS = {
  bukhari: 'صحيح البخاري',
  muslim: 'صحيح مسلم',
  tirmidhi: 'سنن الترمذي',
  abudawud: 'سنن أبي داود',
  nasai: 'سنن النسائي',
  ibnmajah: 'سنن ابن ماجه'
};

const CONTENT_DATA = {
  verses: [{
    text: 'إِنَّ هَٰذِهِ أُمَّتُكُمْ أُمَّةً وَاحِدَةً وَأَنَا رَبُّكُمْ فَاعْبُدُونِ',
    source: 'سورة الأنبياء - الآية ٩٢'
  }],
  hadiths: [
    {
      text: 'قال رسول الله ﷺ: "إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى"',
      source: 'صحيح البخاري - الحديث ١'
    },
    {
      text: 'قال رسول الله ﷺ: "كُنْ فِي الدُّنْيَا كَأَنَّكَ غَرِيبٌ أَوْ عَابِرُ سَبِيلٍ"',
      source: 'صحيح البخاري - الحديث ٦٤١٦'
    }
  ]
};

const toArabicNumerals = number => {
  if (number == null) return '';
  return String(number).replace(/\d/g, d => LOCALIZATION.arabicDigits[parseInt(d, 10)]);
};

const isValidTimeString = timeStr =>
  timeStr && typeof timeStr === 'string' && timeStr.split(':').length === 2;

const parseTimeComponents = timeStr => {
  const [h, m] = timeStr.split(':').map(Number);
  return { hour: h, minute: m, isValid: !isNaN(h) && !isNaN(m) && h >= 0 && h <= 23 && m >= 0 && m <= 59 };
};

const formatArabicTime = timeStr => {
  if (!isValidTimeString(timeStr)) return '--:--';

  const { hour, minute, isValid } = parseTimeComponents(timeStr);
  if (!isValid) return '--:--';

  const period = hour >= 12 ? 'م' : 'ص';
  const displayHour = hour === 0 ? 12 : (hour > 12 ? hour - 12 : hour);
  return `${toArabicNumerals(displayHour)}:${toArabicNumerals(String(minute).padStart(2, '0'))} ${period}`;
};

const timeToMinutes = timeStr => {
  if (!isValidTimeString(timeStr)) return null;

  const { hour, minute, isValid } = parseTimeComponents(timeStr);
  return isValid ? hour * 60 + minute : null;
};

const minutesToTime = minutes => {
  if (typeof minutes !== 'number' || minutes < 0 || minutes >= CONFIG.minutesPerDay) return null;

  const hour = Math.floor(minutes / 60);
  const minute = minutes % 60;
  return `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;
};

const createBeirutTimeFormatter = () => new Intl.DateTimeFormat('en-CA', {
  timeZone: CONFIG.timezone,
  year: 'numeric', month: '2-digit', day: '2-digit',
  hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
});

const parseDateParts = parts => parts.reduce((acc, part) => {
  acc[part.type] = part.value;
  return acc;
}, {});

const getBeirutTime = (date = new Date()) => {
  const validDate = date instanceof Date && !isNaN(date.getTime()) ? date : new Date();

  try {
    const formatter = createBeirutTimeFormatter();
    const parts = parseDateParts(formatter.formatToParts(validDate));

    return new Date(
      parseInt(parts.year, 10),
      parseInt(parts.month, 10) - 1,
      parseInt(parts.day, 10),
      parseInt(parts.hour, 10),
      parseInt(parts.minute, 10),
      parseInt(parts.second, 10)
    );
  } catch {
    return new Date();
  }
};

const getCurrentBeirutMinutes = () => {
  const now = getBeirutTime();
  return now.getHours() * 60 + now.getMinutes();
};

const formatGregorianDateDisplay = () => {
  const now = getBeirutTime();
  const day = toArabicNumerals(now.getDate());
  const month = LOCALIZATION.arabicMonths[now.getMonth()];
  const year = toArabicNumerals(now.getFullYear());
  return `${day} ${month} ${year} م`;
};

const clampValue = (value, min, max) => Math.max(min, Math.min(value, max));

const normalizedCyclePosition = (hijriDay, monthLength, visibilityOffset) => {
  const clampedDay = clampValue(hijriDay, 1, monthLength);
  const clampedMonthLength = Math.max(28, monthLength);
  const clampedOffset = clampValue(visibilityOffset, 0, clampedMonthLength / 2);

  const offsetFraction = clampedOffset / clampedMonthLength;
  const cyclePosition = ((clampedDay - 1) / clampedMonthLength) + offsetFraction;
  return (cyclePosition % 1 + 1) % 1;
};

const calculateIllumination = cyclePosition => (1 - Math.cos(2 * Math.PI * cyclePosition)) / 2;

const getPhaseIndex = cyclePosition =>
  MOON_DATA.phaseBoundaries.findIndex(boundary => cyclePosition <= boundary) || 0;

const PHASE_ANGLE_RANGES = [
  { max: 6.25, name: "New Moon" },
  { max: 83.75, name: "Waxing Crescent" },
  { max: 96.25, name: "First Quarter" },
  { max: 173.75, name: "Waxing Gibbous" },
  { max: 186.25, name: "Full Moon" },
  { max: 263.75, name: "Waning Gibbous" },
  { max: 276.25, name: "Last Quarter" },
  { max: 353.75, name: "Waning Crescent" }
];

const getAccuratePhaseFromAngle = phaseAngle => {
  const normalizedAngle = ((phaseAngle % 360) + 360) % 360;
  const phase = PHASE_ANGLE_RANGES.find(range => normalizedAngle <= range.max);
  return phase ? phase.name : "New Moon";
};

const getMoonIllumination = (hijriDay, monthLength = 29.53, visibilityOffset = 1.5) => {
  const clampedDay = clampValue(hijriDay, 1, monthLength);
  const cyclePosition = normalizedCyclePosition(clampedDay, monthLength, visibilityOffset);
  const illumination = calculateIllumination(cyclePosition);

  return {
    illumination,
    cyclePosition,
    moonAge: (clampedDay - 1) + 0.5,
    dayInMonth: clampedDay
  };
};

const determinePhaseNameForHijriDay = (accuratePhaseName, hijriDay, phaseIndex) =>
  hijriDay === 1 && (accuratePhaseName === "Waxing Crescent" || phaseIndex === 1)
    ? "New Crescent"
    : accuratePhaseName;

const getMoonPhase = (hijriDay, monthLength = 29.53, visibilityOffset = 1.5) => {
  const data = getMoonIllumination(hijriDay, monthLength, visibilityOffset);
  const phaseIndex = getPhaseIndex(data.cyclePosition);
  const illuminationPercent = Math.round(data.illumination * 100);
  const phaseAngle = data.cyclePosition * 360;
  const accuratePhaseName = getAccuratePhaseFromAngle(phaseAngle);
  const phaseName = determinePhaseNameForHijriDay(accuratePhaseName, hijriDay, phaseIndex);

  return {
    dayInMonth: data.dayInMonth,
    phaseName,
    traditionalPhaseName: MOON_DATA.phaseNames[phaseIndex],
    illuminationPercent,
    moonAge: Math.round(data.moonAge * 10) / 10,
    isVisible: illuminationPercent > 0.5,
    phaseEmoji: MOON_DATA.phases[phaseIndex],
    cyclePosition: data.cyclePosition,
    phaseAngle: Math.round(phaseAngle * 10) / 10,
    shadowPosition: calculateShadowPosition(data.cyclePosition, illuminationPercent)
  };
};

const SHADOW_PHASES = [
  { max: 0.125, direction: 'right', offsetMultiplier: 0.9, terminator: 'curved-right', useMax: true },
  { max: 0.375, direction: 'right', offsetMultiplier: 0.8, terminator: 'straight-vertical' },
  { max: 0.5, direction: 'right', offsetMultiplier: 0.7, terminator: 'curved-left' },
  { max: 0.625, direction: 'left', offsetMultiplier: 0.7, terminator: 'curved-right' },
  { max: 0.875, direction: 'left', offsetMultiplier: 0.8, terminator: 'straight-vertical' },
  { max: 1, direction: 'left', offsetMultiplier: 0.9, terminator: 'curved-left', useMax: true }
];

const calculateShadowPosition = (cyclePosition, illuminationPercent) => {
  const shadowWidth = 1 - (illuminationPercent / 100);
  const shadowPhase = SHADOW_PHASES.find(phase => cyclePosition <= phase.max);

  if (!shadowPhase) return null;

  const shadowOffset = shadowPhase.useMax
    ? Math.max(0.1, shadowWidth * shadowPhase.offsetMultiplier)
    : shadowWidth * shadowPhase.offsetMultiplier;

  return {
    direction: shadowPhase.direction,
    width: shadowWidth,
    offset: shadowOffset,
    isWaxing: cyclePosition < 0.5,
    terminatorPosition: shadowPhase.terminator,
    phaseAngle: cyclePosition * 2 * Math.PI
  };
};

const CRESCENT_VISIBILITY_THRESHOLDS = {
  minAgeHours: 18,
  minIllumination: 0.5
};

const isCrescentVisible = (hijriDay, monthLength = 29.53, visibilityOffset = 1.5) => {
  const data = getMoonIllumination(hijriDay, monthLength, visibilityOffset);
  const ageInHours = data.moonAge * 24;
  const illuminationPercent = Math.round(data.illumination * 100);

  return {
    isVisible: ageInHours >= CRESCENT_VISIBILITY_THRESHOLDS.minAgeHours &&
               illuminationPercent >= CRESCENT_VISIBILITY_THRESHOLDS.minIllumination,
    moonAgeHours: Math.round(ageInHours),
    illuminationPercent
  };
};

const createHijriApiUrl = dateKey => {
  const [year, month, day] = dateKey.split('-');
  return `https://api.aladhan.com/v1/gToH?date=${day}-${month}-${year}`;
};

const formatDateKey = date => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided');
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const fetchHijriFromAPI = async gregorianDate => {
  const dateKey = formatDateKey(gregorianDate);
  const url = createHijriApiUrl(dateKey);
  const response = await fetch(url);

  if (!response.ok) throw new Error('API request failed');

  const data = await response.json();
  return extractHijriData(data);
};

const validateHijriDateValues = (day, month, year) =>
  !isNaN(day) && !isNaN(month) && !isNaN(year) &&
  day >= 1 && day <= 30 &&
  month >= 1 && month <= 12 &&
  year >= 1;

const extractHijriData = apiData => {
  if (!apiData?.data?.hijri) throw new Error('Invalid API response format');

  const hijri = apiData.data.hijri;
  const day = parseInt(hijri.day, 10);
  const month = parseInt(hijri.month?.number, 10);
  const year = parseInt(hijri.year, 10);
  const monthLength = parseInt(hijri.month?.days, 10);

  if (!validateHijriDateValues(day, month, year)) {
    throw new Error('Invalid Hijri date data from API');
  }

  return {
    day,
    month,
    year,
    monthLength: isNaN(monthLength) ? 29 : clampValue(monthLength, 29, 30)
  };
};

const isAfterGregorianReform = (year, month, day) =>
  year > 1582 ||
  (year === 1582 && month > 10) ||
  (year === 1582 && month === 10 && day >= 15);

const calculateGregorianCorrection = (year, month, day, centuryFactor) =>
  isAfterGregorianReform(year, month, day)
    ? 2 - centuryFactor + Math.floor(centuryFactor / 4)
    : 0;

const gregorianToJulian = date => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided');
  }

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  const adjustedYear = month <= 2 ? year - 1 : year;
  const adjustedMonth = month <= 2 ? month + 12 : month;
  const centuryFactor = Math.floor(adjustedYear / 100);
  const gregorianCorrection = calculateGregorianCorrection(year, month, day, centuryFactor);

  return Math.floor(365.25 * (adjustedYear + 4716)) +
    Math.floor(30.6001 * (adjustedMonth + 1)) +
    day + gregorianCorrection - 1524.5;
};

const julianToHijri = julianDay => {
  const daysSinceEpoch = julianDay - CONFIG.islamicEpochJulian;
  const islamicYear = Math.floor((30 * daysSinceEpoch + 10646) / 10631);
  const yearStartDays = Math.floor((islamicYear * 10631 - 10646) / 30);
  const dayOfYear = Math.floor(daysSinceEpoch - yearStartDays) + 1;
  const monthData = calculateIslamicMonth(islamicYear, dayOfYear);

  return {
    day: clampValue(monthData.dayOfMonth, 1, monthData.monthLength),
    month: monthData.month,
    year: Math.max(1, islamicYear),
    monthLength: monthData.monthLength
  };
};

const generateMonthLengths = isLeapYear =>
  [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, isLeapYear ? 30 : 29];

const calculateIslamicMonth = (islamicYear, dayOfYear) => {
  const yearInCycle = ((islamicYear - 1) % 30) + 1;
  const isLeapYear = CONFIG.leapCycle.includes(yearInCycle);
  const monthLengths = generateMonthLengths(isLeapYear);

  let remainingDays = dayOfYear;

  for (let monthIndex = 0; monthIndex < 12; monthIndex++) {
    if (remainingDays <= monthLengths[monthIndex]) {
      return {
        month: monthIndex + 1,
        dayOfMonth: remainingDays || 1,
        monthLength: monthLengths[monthIndex]
      };
    }
    remainingDays -= monthLengths[monthIndex];
  }

  return { month: 12, dayOfMonth: 1, monthLength: monthLengths[11] };
};

const calculateTabularHijri = (gregorianDate, maghribTime) => {
  if (!(gregorianDate instanceof Date) || isNaN(gregorianDate.getTime())) {
    throw new Error('Invalid date provided');
  }
  const baseHijri = julianToHijri(gregorianToJulian(gregorianDate));
  return shouldAdvanceToNextHijriDay(gregorianDate, maghribTime) ? getNextHijriDay(gregorianDate) : baseHijri;
};

const shouldAdvanceToNextHijriDay = (gregorianDate, maghribTime) => {
  if (!maghribTime || !gregorianDate) return false;
  
  const maghribMinutes = timeToMinutes(maghribTime);
  if (maghribMinutes === null) return false;
  
  const currentBeirutMinutes = getCurrentBeirutMinutes();
  return currentBeirutMinutes >= maghribMinutes;
};

const getCurrentMinutes = date => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided');
  }
  return date.getHours() * 60 + date.getMinutes();
};

const getNextHijriDay = gregorianDate => {
  if (!(gregorianDate instanceof Date) || isNaN(gregorianDate.getTime())) {
    throw new Error('Invalid date provided');
  }
  const nextGregorianDate = new Date(gregorianDate);
  nextGregorianDate.setDate(nextGregorianDate.getDate() + 1);
  return julianToHijri(gregorianToJulian(nextGregorianDate));
};

const determineQueryDate = (gregorianDate, maghribTime) => {
  if (!(gregorianDate instanceof Date) || isNaN(gregorianDate.getTime())) {
    throw new Error('Invalid date provided');
  }

  if (shouldAdvanceToNextHijriDay(gregorianDate, maghribTime)) {
    const queryDate = new Date(gregorianDate);
    queryDate.setDate(queryDate.getDate() + 1);
    return queryDate;
  }
  return new Date(gregorianDate);
};

const getHijriDate = async (gregorianDate, maghribTime) => {
  if (!(gregorianDate instanceof Date) || isNaN(gregorianDate.getTime())) {
    throw new Error('Invalid date provided');
  }
  try {
    const queryDate = determineQueryDate(gregorianDate, maghribTime);
    return await fetchHijriFromAPI(queryDate);
  } catch {
    return calculateTabularHijri(gregorianDate, maghribTime);
  }
};

const PRAYER_VALIDATION = {
  required: ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'],
  optional: ['Sunrise', 'Firstthird', 'Lastthird']
};

const validateTimeString = timeStr =>
  timeStr && typeof timeStr === 'string' && timeStr.includes(':');

const validateTimeMinutes = minutes =>
  minutes !== null && minutes >= 0 && minutes < CONFIG.minutesPerDay;

const createTimeMap = (times, prayers) => {
  const timeMap = {};

  for (const prayer of prayers) {
    if (!times[prayer]) continue;

    const minutes = timeToMinutes(times[prayer]);
    if (!validateTimeMinutes(minutes)) return null;

    timeMap[prayer] = minutes;
  }

  return timeMap;
};

const validatePrayerSequence = timeMap => {
  const { Fajr, Dhuhr, Asr, Maghrib, Isha, Sunrise } = timeMap;

  const daySequenceValid = Fajr < Dhuhr && Dhuhr < Asr && Asr < Maghrib;
  const ishaValid = Isha > Maghrib || Isha < Fajr;
  const sunriseValid = !Sunrise || (Fajr < Sunrise && Sunrise < Dhuhr);

  return daySequenceValid && ishaValid && sunriseValid;
};

const validatePrayerTimes = times => {
  const allPrayers = [...PRAYER_VALIDATION.required, ...PRAYER_VALIDATION.optional];

  for (const prayer of PRAYER_VALIDATION.required) {
    if (!validateTimeString(times[prayer])) return false;
  }

  for (const prayer of PRAYER_VALIDATION.optional) {
    if (times[prayer] && !validateTimeString(times[prayer])) return false;
  }

  const timeMap = createTimeMap(times, allPrayers);
  return timeMap && validatePrayerSequence(timeMap);
};

const calculateTimeDifference = (targetMinutes, currentMinutes) => {
  let diff = targetMinutes - currentMinutes;
  return diff <= 0 ? diff + CONFIG.minutesPerDay : diff;
};

const findNextPrayer = (currentMinutes, prayerMinutes) => {
  const prayers = PRAYER_VALIDATION.required;
  let nextPrayer = prayers[0];
  let minDiff = Infinity;

  for (const prayer of prayers) {
    if (prayerMinutes[prayer] == null) continue;

    const diff = calculateTimeDifference(prayerMinutes[prayer], currentMinutes);
    if (diff < minDiff) {
      minDiff = diff;
      nextPrayer = prayer;
    }
  }

  return nextPrayer;
};

const formatTimeRemaining = (hours, minutes) => {
  let text = '';
  if (hours > 0) text += `${toArabicNumerals(hours)} ساعة `;
  if (minutes > 0 || hours === 0) text += `${toArabicNumerals(minutes)} دقيقة `;
  return text.trim();
};

const calculateTimeRemaining = (currentMinutes, prayerMinutes) => {
  const diff = calculateTimeDifference(prayerMinutes, currentMinutes);
  const hours = Math.floor(diff / 60);
  const minutes = diff % 60;
  return formatTimeRemaining(hours, minutes);
};

const updateElementContent = (selector, content, useHTML = false) => {
  const element = document.querySelector(selector);
  if (!element || content == null) return;

  if (useHTML) {
    element.innerHTML = String(content);
  } else {
    element.textContent = String(content);
  }
};

const renderGregorianDate = () => {
  updateElementContent('.date-row:nth-child(2) .date-value', formatGregorianDateDisplay());
};

const createMoonTooltip = (moonData, crescentInfo) => {
  const visibilityText = crescentInfo.isVisible ? 'مرئي' : 'غير مرئي';
  const ageText = toArabicNumerals(Math.round(moonData.moonAge));
  const angleText = toArabicNumerals(moonData.phaseAngle);

  return `${moonData.phaseName} - ${moonData.illuminationPercent}% مضاء - عمر القمر: ${ageText} يوم - الهلال: ${visibilityText} - زاوية الطور: ${angleText}°`;
};

const createMoonCanvas = (canvasId, tooltipText) => `
  <span class="moon-phase" title="${tooltipText}">
    <canvas id="${canvasId}" class="moon-canvas" width="32" height="32"></canvas>
  </span>
`;

const generateUniqueCanvasId = () =>
  `moon-canvas-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

const renderMoonVisualization = (canvasId, moonData) => {
  const moonRenderer = moonRendererManager.getInstance();
  if (!moonRenderer?.drawMoon) return;

  const canvas = document.getElementById(canvasId);
  if (!canvas) return;

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, canvas.width, canvas.height);

  try {
    const phaseAngle = (moonData.cyclePosition * 360) % 360;
    moonRenderer.drawMoon(phaseAngle);

    if (moonRenderer.canvas) {
      ctx.drawImage(moonRenderer.canvas, 0, 0, 32, 32);
    }
  } catch (error) {
    // Silent fail for moon rendering
  }
};

const renderHijriDate = hijri => {
  if (!hijri) return;

  try {
    const monthName = LOCALIZATION.hijriMonths[hijri.month - 1] || '';
    const monthLength = hijri.monthLength || 29.53;
    const moonData = getMoonPhase(hijri.day, monthLength);
    const crescentInfo = isCrescentVisible(hijri.day, monthLength);

    const tooltipText = createMoonTooltip(moonData, crescentInfo);
    const canvasId = generateUniqueCanvasId();
    const moonDisplay = createMoonCanvas(canvasId, tooltipText);

    const hijriDateText = `${toArabicNumerals(hijri.day)} ${monthName} ${toArabicNumerals(hijri.year)} ${moonDisplay}`;
    updateElementContent('.date-row:nth-child(1) .date-value', hijriDateText, true);

    setTimeout(() => renderMoonVisualization(canvasId, moonData), 0);
  } catch (error) {
    console.error('Error rendering Hijri date:', error);
  }
};

const formatTimeComponent = value => String(value).padStart(2, '0');

const renderCurrentTime = () => {
  const now = getBeirutTime();
  const hour = now.getHours();
  const minute = formatTimeComponent(now.getMinutes());
  const second = formatTimeComponent(now.getSeconds());
  const period = hour >= 12 ? 'مساءً' : 'صباحاً';
  const displayHour = hour % 12 || 12;
  const weekday = LOCALIZATION.weekdays[now.getDay()];

  const timeText = `<i class="fas fa-calendar-check"></i> ${weekday}، ${toArabicNumerals(displayHour)}:${toArabicNumerals(minute)}:${toArabicNumerals(second)} ${period}`;
  updateElementContent('.current-time', timeText, true);
};

const appState = {
  hijriDate: null,
  prayerTimes: null,
  lastHijriCheckDate: null,
  lastMaghribTransition: false,
  updateInterval: null,
  lastLocationCheck: 0,
  locationCheckInterval: null
};

const isNewDay = today => !appState.lastHijriCheckDate || appState.lastHijriCheckDate !== today;

const shouldRefreshForNewDay = today => {
  if (isNewDay(today)) {
    appState.lastMaghribTransition = false;
    return true;
  }
  return false;
};

const shouldRefreshForMaghribTransition = () => {
  if (!appState.prayerTimes?.Maghrib) return false;

  const maghribMinutes = timeToMinutes(appState.prayerTimes.Maghrib);
  if (maghribMinutes === null) return false;

  const currentMinutes = getCurrentBeirutMinutes();
  const isAfterMaghrib = currentMinutes >= maghribMinutes;

  if (isAfterMaghrib && !appState.lastMaghribTransition) {
    appState.lastMaghribTransition = true;
    return true;
  }

  if (!isAfterMaghrib && appState.lastMaghribTransition) {
    appState.lastMaghribTransition = false;
  }

  return false;
};

const shouldRefreshHijriDate = () => {
  const today = getBeirutTime().toDateString();
  return shouldRefreshForNewDay(today) || shouldRefreshForMaghribTransition();
};

const updateTimeDisplays = () => {
  renderCurrentTime();
  renderGregorianDate();
};

const updateHijriDisplay = () => {
  if (appState.hijriDate) {
    renderHijriDate(appState.hijriDate);
  }
};

const updatePrayerDisplay = () => {
  if (appState.prayerTimes && validatePrayerTimes(appState.prayerTimes)) {
    updatePrayerTimesDisplay();
  }
};

const updateAllDisplays = async () => {
  try {
    updateTimeDisplays();

    if (!appState.prayerTimes) {
      await initializeData();
    }

    if (shouldRefreshHijriDate()) {
      await refreshHijriDate();
    }

    updatePrayerDisplay();
    updateHijriDisplay();

  } catch (error) {
    console.error('Error updating displays:', error);
  }
};

const initializeData = async () => {
  try {
    const now = getBeirutTime();

    // Use the centralized location data manager for all location-dependent data
    const data = await LocationDataManager.updateLocationData(true); // Force refresh on init

    if (data.prayerTimes) {
      appState.prayerTimes = data.prayerTimes;
    }

    // Update Hijri date with location-aware data
    if (data.hijriDate) {
      const hijriDisplayData = createHijriDisplayData(data.hijriDate);
      if (hasHijriDateChanged(appState.hijriDate, hijriDisplayData)) {
        appState.hijriDate = hijriDisplayData;
        renderHijriDate(data.hijriDate);
      }
    } else {
      await refreshHijriDate();
    }

  } catch (error) {
    console.error('Error initializing data:', error);
  }
};

const updateHijriDateState = now => {
  appState.lastHijriCheckDate = now.toDateString();
};

const hasHijriDateChanged = (current, newData) =>
  !current ||
  current.day !== newData.day ||
  current.month !== newData.month ||
  current.year !== newData.year;

const createHijriDisplayData = hijriData => ({
  day: hijriData.day,
  month: hijriData.month,
  year: hijriData.year,
  monthLength: hijriData.monthDays || hijriData.monthLength || 29
});

const refreshHijriDate = async () => {
  try {
    const now = getBeirutTime();
    updateHijriDateState(now);

    const referenceDate = new Date(now);
    referenceDate.setHours(12, 0, 0, 0);
    const maghribTime = appState.prayerTimes?.Maghrib || null;
    const newHijriData = await getHijriDate(referenceDate, maghribTime);

    if (hasHijriDateChanged(appState.hijriDate, newHijriData)) {
      appState.hijriDate = createHijriDisplayData(newHijriData);
    }

  } catch (error) {
    console.error('Error refreshing Hijri date:', error);
  }
};

const createPrayerMinutesMap = prayerTimes => {
  const prayerMinutes = {};

  for (const prayer of PRAYER_VALIDATION.required) {
    const minutes = timeToMinutes(prayerTimes[prayer]);
    if (minutes !== null) {
      prayerMinutes[prayer] = minutes;
    }
  }

  return prayerMinutes;
};

const updatePrayerTimeElements = prayerTimes => {
  Object.entries(prayerTimes).forEach(([prayer, time]) => {
    const selector = `.prayer-${prayer.toLowerCase()} .time`;
    updateElementContent(selector, toArabicNumerals(time));
  });
};

const updateNextPrayerIndicator = (nextPrayer, timeRemaining) => {
  const prayerName = LOCALIZATION.prayerNames[nextPrayer] || nextPrayer;
  updateElementContent('.next-prayer', `${prayerName} - ${timeRemaining}`);
};

const updatePrayerTimesDisplay = () => {
  if (!appState.prayerTimes || !validatePrayerTimes(appState.prayerTimes)) {
    return;
  }

  const currentMinutes = getCurrentBeirutMinutes();
  const prayerMinutes = createPrayerMinutesMap(appState.prayerTimes);
  const nextPrayer = findNextPrayer(currentMinutes, prayerMinutes);
  const timeRemaining = calculateTimeRemaining(currentMinutes, prayerMinutes[nextPrayer]);

  updatePrayerTimeElements(appState.prayerTimes);
  updateNextPrayerIndicator(nextPrayer, timeRemaining);
};

const performPeriodicUpdates = () => {
  renderCurrentTime();

  if (shouldRefreshHijriDate()) {
    refreshHijriDate();
  }

  if (appState.prayerTimes && validatePrayerTimes(appState.prayerTimes)) {
    updatePrayerTimesDisplay();
  }
};

const startAutoUpdate = () => {
  if (appState.updateInterval) {
    clearInterval(appState.updateInterval);
  }

  if (appState.locationCheckInterval) {
    clearInterval(appState.locationCheckInterval);
  }

  initializeData().then(() => {
    appState.updateInterval = setInterval(performPeriodicUpdates, 1000);

    // Set up location monitoring every 5 minutes
    appState.locationCheckInterval = setInterval(async () => {
      try {
        const now = Date.now();
        // Only check location every 5 minutes to avoid excessive API calls
        if (now - appState.lastLocationCheck > 300000) {
          appState.lastLocationCheck = now;

          const locationInfo = await getLocationWithFallback();
          if (LocationDataManager.hasLocationChanged(locationInfo)) {
            console.log('Location change detected, updating all data...');
            const updatedData = await LocationDataManager.updateLocationData(true);

            if (updatedData.prayerTimes) {
              appState.prayerTimes = updatedData.prayerTimes;
              updatePrayerTimesDisplay();
            }

            if (updatedData.hijriDate) {
              const hijriDisplayData = createHijriDisplayData(updatedData.hijriDate);
              if (hasHijriDateChanged(appState.hijriDate, hijriDisplayData)) {
                appState.hijriDate = hijriDisplayData;
                renderHijriDate(updatedData.hijriDate);
              }
            }
          }
        }
      } catch (error) {
        console.error('Failed to check location updates:', error);
      }
    }, 60000); // Check every minute, but only perform location check every 5 minutes
  });
};

const stopAutoUpdate = () => {
  if (appState.updateInterval) {
    clearInterval(appState.updateInterval);
    appState.updateInterval = null;
  }

  if (appState.locationCheckInterval) {
    clearInterval(appState.locationCheckInterval);
    appState.locationCheckInterval = null;
  }
};

const initializeApp = () => {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startAutoUpdate);
  } else {
    startAutoUpdate();
  }
};

const handleVisibilityChange = () => {
  if (document.hidden) {
    stopAutoUpdate();
  } else {
    startAutoUpdate();
  }
};

const setupEventListeners = () => {
  document.addEventListener('visibilitychange', handleVisibilityChange);
};

if (typeof document !== 'undefined') {
  initializeApp();
  setupEventListeners();
}

class CelestialProgressIndicator {
  constructor() {
    this.progressIndicator = null;
    this.celestialBody = null;
    this.prayerTimes = {};
    this.prayerAngles = {};
    this.isInitialized = false;
    this.updateInterval = null;
    this.resizeTimeout = null;
    this.lastCelestialType = null;

    this.handleResize = this.handleResize.bind(this);
    this.update = this.update.bind(this);
  }

  isValidTimeFormat = timeString => 
    timeString && typeof timeString === 'string' && 
    /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeString.trim());

  validatePrayerTimes = prayerTimes => {
    const requiredPrayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
    const errors = [];

    if (!prayerTimes || typeof prayerTimes !== 'object') {
      throw new Error('Prayer times must be a valid object');
    }

    for (const prayer of requiredPrayers) {
      if (!prayerTimes[prayer]) {
        errors.push(`Missing prayer time: ${prayer}`);
      } else if (!this.isValidTimeFormat(prayerTimes[prayer])) {
        errors.push(`Invalid time format for ${prayer}: ${prayerTimes[prayer]}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Prayer time validation failed: ${errors.join(', ')}`);
    }

    return true;
  };

  getCircleDimensions = () => {
    const container = document.querySelector('.progress-container');
    if (!container) return null;

    const rect = container.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const radius = Math.min(rect.width, rect.height) * 0.34;

    return {
      centerX,
      centerY,
      radius,
      containerWidth: rect.width,
      containerHeight: rect.height
    };
  };

  calculatePrayerAngles = (prayerTimes) => {
    const prayerKeys = [];
    const basePrayers = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
    prayerKeys.push(...basePrayers);

    // Add night third prayers if available
    if (prayerTimes.Firstthird) prayerKeys.push('Firstthird');
    if (prayerTimes.Lastthird) prayerKeys.push('Lastthird');

    const requiredPrayers = ['Fajr', 'Maghrib'];
    if (!requiredPrayers.every(prayer => {
      const prayerTime = prayerTimes[prayer];
      return prayerTime && this.timeToMinutes(prayerTime) !== -1;
    })) {
      const angleIncrement = 360 / prayerKeys.length;
      return prayerKeys.map((_, index) => index * angleIncrement);
    }

    const prayerTimesInMinutes = prayerKeys.map(key => this.timeToMinutes(prayerTimes[key]));
    const fajrMinutes = this.timeToMinutes(prayerTimes.Fajr);
    const maghribMinutes = this.timeToMinutes(prayerTimes.Maghrib);
    
    if (fajrMinutes === maghribMinutes) {
      const angleIncrement = 360 / prayerKeys.length;
      return prayerKeys.map((_, index) => index * angleIncrement);
    }

    const nightDuration = (fajrMinutes - maghribMinutes + 1440) % 1440;
    const dayDuration = 1440 - nightDuration;

    if (nightDuration <= 0 || dayDuration <= 0) {
      const angleIncrement = 360 / prayerKeys.length;
      return prayerKeys.map((_, index) => index * angleIncrement);
    }

    return prayerTimesInMinutes.map((timeMinutes, index) => {
      if (timeMinutes === -1) {
        return index * (360 / prayerKeys.length);
      }
      
      const isNightTime = timeMinutes >= maghribMinutes || timeMinutes < fajrMinutes;
      
      if (isNightTime) {
        const minutesSinceMaghrib = (timeMinutes - maghribMinutes + 1440) % 1440;
        const nightProgressFraction = minutesSinceMaghrib / nightDuration;
        return 0 + nightProgressFraction * 180;
      } else {
        const minutesSinceFajr = (timeMinutes - fajrMinutes + 1440) % 1440;
        const dayProgressFraction = minutesSinceFajr / dayDuration;
        return 180 + dayProgressFraction * 180;
      }
    });
  };

  init = (prayerTimes) => {
    try {
      this.progressIndicator = document.getElementById('progressIndicator');
      this.celestialBody = document.getElementById('celestialBody');

      if (!this.progressIndicator || !this.celestialBody) {

        return false;
      }

      this.validatePrayerTimes(prayerTimes);
      this.prayerTimes = { ...prayerTimes };
      this.prayerAngles = this.calculatePrayerAngles(prayerTimes);
      this.celestialBody.className = 'celestial-body day-sun';
      this.isInitialized = true;
      this.startUpdates();
      return true;
    } catch (error) {
      console.error('Failed to initialize celestial indicator:', error);
      return false;
    }
  };

  timeToMinutes = timeString => {
    if (!timeString || typeof timeString !== 'string') return -1;
    const parts = timeString.split(':');
    if (parts.length !== 2) return -1;
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) return -1;
    return hours * 60 + minutes;
  };

  getCurrentSegment = () => {
    const now = typeof getBeirutTime === 'function' ? getBeirutTime() : new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes() + now.getSeconds() / 60;
    
    const prayerKeys = [];
    const basePrayers = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
    prayerKeys.push(...basePrayers);

    // Add night third prayers if available
    if (this.prayerTimes.Firstthird) prayerKeys.push('Firstthird');
    if (this.prayerTimes.Lastthird) prayerKeys.push('Lastthird');
    
    const mainPrayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
    
    const times = { ...this.prayerTimes };
    if (!times.Sunrise) {
      const fajrMinutes = this.timeToMinutes(times.Fajr);
      const sunriseMinutes = (fajrMinutes + 30) % 1440;
      times.Sunrise = `${Math.floor(sunriseMinutes / 60).toString().padStart(2, '0')}:${(sunriseMinutes % 60).toString().padStart(2, '0')}`;
    }
    
    for (let i = 0; i < prayerKeys.length; i++) {
      const currentKey = prayerKeys[i];
      const nextKey = prayerKeys[(i + 1) % prayerKeys.length];
      
      if (!times[currentKey] || !times[nextKey]) continue;
      
      const currentTime = this.timeToMinutes(times[currentKey]);
      const nextTime = this.timeToMinutes(times[nextKey]);
      
      if (currentTime === -1 || nextTime === -1) continue;
      
      const currentAngle = this.prayerAngles[i] || 0;
      const nextAngle = this.prayerAngles[(i + 1) % this.prayerAngles.length] || 0;

      const isInSegment = nextTime < currentTime
        ? currentMinutes >= currentTime || currentMinutes < nextTime
        : currentMinutes >= currentTime && currentMinutes < nextTime;

      if (isInSegment) {
        let duration = nextTime - currentTime;
        if (duration <= 0) duration += 1440;
        let elapsed = currentMinutes - currentTime;
        if (elapsed < 0) elapsed += 1440;
        const progress = duration > 0 ? Math.max(0, Math.min(elapsed / duration, 1)) : 0;

        const currentPrayer = mainPrayers.includes(currentKey) ? currentKey : 
                             (currentKey === 'Sunrise' ? 'Fajr' : 
                              currentKey === 'Lastthird' ? 'Isha' : 'Fajr');

        return {
          current: currentPrayer,
          next: mainPrayers[mainPrayers.indexOf(currentPrayer) + 1] || mainPrayers[0],
          progress,
          currentAngle,
          nextAngle
        };
      }
    }

    return this.getDefaultSegment();
  };

  getDefaultSegment = () => ({
    current: 'Fajr',
    next: 'Dhuhr',
    progress: 0,
    currentAngle: this.prayerAngles[0] || 90,
    nextAngle: this.prayerAngles[2] || 180
  });

  interpolateAngle = (startAngle, endAngle, progress) => {
    startAngle = Number(startAngle) || 0;
    endAngle = Number(endAngle) || 0;
    progress = Math.max(0, Math.min(Number(progress) || 0, 1));
    
    startAngle = ((startAngle % 360) + 360) % 360;
    endAngle = ((endAngle % 360) + 360) % 360;

    let diff = endAngle - startAngle;
    if (diff > 180) diff -= 360;
    if (diff < -180) diff += 360;

    const currentAngle = startAngle + diff * progress;
    return ((currentAngle % 360) + 360) % 360;
  };

  calculatePosition = (angle, dimensions) => {
    if (!dimensions) return { x: 0, y: 0 };
    
    const adjustedAngleRadians = ((Number(angle) - 90) * Math.PI) / 180;
    const x = dimensions.centerX + dimensions.radius * Math.cos(adjustedAngleRadians);
    const y = dimensions.centerY + dimensions.radius * Math.sin(adjustedAngleRadians);
    return { x, y };
  };

  getCelestialBodyType = () => {
    const now = typeof getBeirutTime === 'function' ? getBeirutTime() : new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    
    const fajrMinutes = this.timeToMinutes(this.prayerTimes.Fajr);
    const sunriseMinutes = this.prayerTimes.Sunrise ? this.timeToMinutes(this.prayerTimes.Sunrise) : fajrMinutes + 30;
    const maghribMinutes = this.timeToMinutes(this.prayerTimes.Maghrib);
    const ishaMinutes = this.timeToMinutes(this.prayerTimes.Isha);
    
    if (fajrMinutes === -1 || maghribMinutes === -1 || ishaMinutes === -1) {
      return 'day-sun';
    }
    
    // Handle day/night boundary crossing midnight
    const isDaytime = maghribMinutes > fajrMinutes
      ? (currentMinutes >= fajrMinutes && currentMinutes < maghribMinutes)
      : (currentMinutes >= fajrMinutes || currentMinutes < maghribMinutes);
    
    if (isDaytime) {
      if (currentMinutes >= fajrMinutes && currentMinutes < sunriseMinutes) {
        return 'predawn-moon';
      }
      // One hour before Maghrib shows sunset
      if (currentMinutes >= maghribMinutes - 60) {
        return 'sunset-sun';
      }
      return 'day-sun';
    } else {
      // Night time - check if still in sunset period (until Isha)
      const isInSunsetPeriod = maghribMinutes > ishaMinutes
        ? (currentMinutes >= maghribMinutes || currentMinutes < ishaMinutes)
        : (currentMinutes >= maghribMinutes && currentMinutes < ishaMinutes);
      
      return isInSunsetPeriod ? 'sunset-sun' : 'night-moon';
    }
  };

  update = () => {
    if (!this.isInitialized) return;

    try {
      const dimensions = this.getCircleDimensions();
      if (!dimensions) return;

      const segment = this.getCurrentSegment();
      const currentAngle = this.interpolateAngle(segment.currentAngle, segment.nextAngle, segment.progress);
      const position = this.calculatePosition(currentAngle, dimensions);

      const indicatorSize = this.progressIndicator.offsetWidth || 40;
      const left = position.x - indicatorSize / 2;
      const top = position.y - indicatorSize / 2;

      this.progressIndicator.style.left = `${left}px`;
      this.progressIndicator.style.top = `${top}px`;
      this.progressIndicator.style.transform = 'none';

      const celestialType = this.getCelestialBodyType();
      if (this.lastCelestialType !== celestialType) {
        this.celestialBody.className = `celestial-body ${celestialType}`;
        this.lastCelestialType = celestialType;
      }
    } catch (error) {
      console.error('Error during update:', error);
    }
  };

  startUpdates = () => {
    this.update();
    this.updateInterval = setInterval(this.update, 1000);
    window.addEventListener('resize', this.handleResize);
  };

  handleResize = () => {
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }
    this.resizeTimeout = setTimeout(() => {
      this.update();
    }, 100);
  };

  destroy = () => {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }
    window.removeEventListener('resize', this.handleResize);
    this.isInitialized = false;
  };
}

const celestialIndicatorManager = {
  instance: null,

  initialize(prayerTimes) {
    try {
      if (this.instance) {
        this.instance.destroy();
      }
      this.instance = new CelestialProgressIndicator();
      const success = this.instance.init(prayerTimes);
      if (!success) {
        this.instance = null;
      }
      return this.instance;
    } catch (error) {
      console.error('Failed to initialize celestial indicator:', error);
      return null;
    }
  },

  getInstance() {
    return this.instance;
  },

  forceUpdate() {
    if (this.instance) {
      this.instance.update();
      return true;
    }
    return false;
  }
};

// Cache DOM queries to improve performance
const TIMELINE_CONSTANTS = {
  SVG_SIZE: 1000,
  CENTER_Y_OFFSET: 0.46,
  ORBIT_RADIUS: 340,
  MINUTES_PER_DAY: 1440,
  NIGHT_ARC_START: 0,      // Night: 0° to 180° (12 hours)
  DAY_ARC_START: 180,      // Day: 180° to 360° (12 hours)
  ARC_DEGREES: 180,        // Each arc spans 180°
  MAGHRIB_REFERENCE_OFFSET: 90
};

const prayerPointSizeManager = {
  cache: null,

  getSize() {
    if (this.cache !== null) return this.cache;

    const measurementElement = document.createElement('div');
    measurementElement.style.display = 'none';
    measurementElement.className = 'prayer-point';
    document.body.appendChild(measurementElement);
    this.cache = parseFloat(getComputedStyle(measurementElement).width) || 60;
    document.body.removeChild(measurementElement);
    return this.cache;
  },

  clearCache() {
    this.cache = null;
  }
};

const getPrayerPointsRect = () => {
  const containerElement = document.getElementById('prayer-points-dynamic');
  if (!containerElement) return { width: 380, height: 380 };
  const rect = containerElement.getBoundingClientRect();
  return { width: rect.width, height: rect.height };
};

const isValidPrayerTimeData = (times, requiredPrayers) => {
  return requiredPrayers.every(prayer => {
    const prayerTime = times[prayer];
    return prayerTime && timeToMinutes(prayerTime) !== -1;
  });
};

const calculateDayNightDurations = (fajrMinutes, maghribMinutes) => {
  const nightDuration = (fajrMinutes - maghribMinutes + TIMELINE_CONSTANTS.MINUTES_PER_DAY) % TIMELINE_CONSTANTS.MINUTES_PER_DAY;
  const dayDuration = TIMELINE_CONSTANTS.MINUTES_PER_DAY - nightDuration;
  return { nightDuration, dayDuration };
};

const isNightTime = (timeMinutes, maghribMinutes, fajrMinutes) => {
  return timeMinutes >= maghribMinutes || timeMinutes < fajrMinutes;
};

const calculateNightAngle = (timeMinutes, maghribMinutes, nightDuration) => {
  const minutesSinceMaghrib = (timeMinutes - maghribMinutes + TIMELINE_CONSTANTS.MINUTES_PER_DAY) % TIMELINE_CONSTANTS.MINUTES_PER_DAY;
  const nightProgressFraction = minutesSinceMaghrib / nightDuration;

  // Night arc: 180° (from 0° to 180°) - representing 12 hours
  return nightProgressFraction * 180;
};

const calculateDayAngle = (timeMinutes, fajrMinutes, dayDuration) => {
  const minutesSinceFajr = (timeMinutes - fajrMinutes + TIMELINE_CONSTANTS.MINUTES_PER_DAY) % TIMELINE_CONSTANTS.MINUTES_PER_DAY;
  const dayProgressFraction = minutesSinceFajr / dayDuration;

  // Day arc: 180° (from 180° to 360°) - representing 12 hours
  return 180 + dayProgressFraction * 180;
};

const createFallbackAngles = (prayerKeys) => {
  const angleIncrement = 360 / prayerKeys.length;
  return prayerKeys.map((_, index) => index * angleIncrement);
};

const getPrayerAngles = times => {
  const prayerKeys = [];
  const basePrayers = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  prayerKeys.push(...basePrayers);

  // Add night third prayers if available
  if (times.Firstthird) prayerKeys.push('Firstthird');
  if (times.Lastthird) prayerKeys.push('Lastthird');

  const requiredPrayers = ['Fajr', 'Maghrib'];
  if (!isValidPrayerTimeData(times, requiredPrayers)) {
    console.error('Missing or invalid required prayer times for angle calculation');
    return createFallbackAngles(prayerKeys);
  }

  const prayerTimesInMinutes = prayerKeys.map(key => timeToMinutes(times[key]));
  const fajrMinutes = timeToMinutes(times.Fajr);
  const maghribMinutes = timeToMinutes(times.Maghrib);
  
  if (fajrMinutes === maghribMinutes) {

    return createFallbackAngles(prayerKeys);
  }

  const { nightDuration, dayDuration } = calculateDayNightDurations(fajrMinutes, maghribMinutes);

  if (nightDuration <= 0 || dayDuration <= 0) {
    console.error('Invalid day/night duration calculation');
    return createFallbackAngles(prayerKeys);
  }

  return prayerTimesInMinutes.map((timeMinutes, index) => {
    if (timeMinutes === -1) {

      return index * (360 / prayerKeys.length);
    }
    
    if (isNightTime(timeMinutes, maghribMinutes, fajrMinutes)) {
      return calculateNightAngle(timeMinutes, maghribMinutes, nightDuration);
    } else {
      return calculateDayAngle(timeMinutes, fajrMinutes, dayDuration);
    }
  });
};

const createSVGDefinitions = () => `
<radialGradient id="fajr-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#fff"/>
  <stop offset="70%" stop-color="#b3d8ff"/>
  <stop offset="100%" stop-color="#3a6ea5"/>
</radialGradient>
<radialGradient id="isha-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#fff"/>
  <stop offset="70%" stop-color="#b3d8ff"/>
  <stop offset="100%" stop-color="#3a6ea5"/>
</radialGradient>
<radialGradient id="sunrise-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#fffbe4"/>
  <stop offset="60%" stop-color="#ffd700"/>
  <stop offset="100%" stop-color="#ff9800"/>
</radialGradient>
<radialGradient id="prayer-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#fff"/>
  <stop offset="70%" stop-color="#ffe9a0"/>
  <stop offset="100%" stop-color="#e0c080"/>
</radialGradient>
<radialGradient id="current-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#fff"/>
  <stop offset="70%" stop-color="#ffe9a0"/>
  <stop offset="100%" stop-color="#ffd700"/>
</radialGradient>
<radialGradient id="firstthird-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#f0f8ff"/>
  <stop offset="70%" stop-color="#c8e3ff"/>
  <stop offset="100%" stop-color="#5a92d9"/>
</radialGradient>
<radialGradient id="lastthird-gradient" cx="50%" cy="50%" r="50%">
  <stop offset="0%" stop-color="#f7fafc"/>
  <stop offset="70%" stop-color="#a0aec0"/>
  <stop offset="100%" stop-color="#4a5568"/>
</radialGradient>
<filter id="fajr-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#3a6ea5" flood-opacity="0.35"/>
</filter>
<filter id="isha-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#3a6ea5" flood-opacity="0.35"/>
</filter>
<filter id="sunrise-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="10" flood-color="#ff9800" flood-opacity="0.45"/>
</filter>
<filter id="prayer-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#ffd700" flood-opacity="0.35"/>
</filter>
<filter id="firstthird-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="8" flood-color="#5a92d9" flood-opacity="0.4"/>
</filter>
<filter id="lastthird-glow" x="-30%" y="-30%" width="160%" height="160%">
  <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#4a5568" flood-opacity="0.3"/>
</filter>
<filter id="current-glow" x="-40%" y="-40%" width="180%" height="180%">
  <feDropShadow dx="0" dy="0" stdDeviation="12" flood-color="#ffd700" flood-opacity="0.55"/>
</filter>`;

const createSunIcon = () => `<g opacity="0.8">
  <circle cx="0" cy="0" r="15" fill="#FFD700"/>
  <g stroke="#FF9800" stroke-width="2">
    <line x1="0" y1="-22" x2="0" y2="-30"/>
    <line x1="0" y1="22" x2="0" y2="30"/>
    <line x1="-22" y1="0" x2="-30" y2="0"/>
    <line x1="22" y1="0" x2="30" y2="0"/>
    <line x1="16" y1="16" x2="22" y2="22"/>
    <line x1="-16" y1="16" x2="-22" y2="22"/>
    <line x1="16" y1="-16" x2="22" y2="-22"/>
    <line x1="-16" y1="-16" x2="-22" y2="-22"/>
  </g>
</g>`;

const createCrescentIcon = () => `<g>
  <path d="M 0 -18 A 18 18 0 1 0 0 18 Q -8 0 0 -18" fill="#b3d8ff" stroke="#3a6ea5" stroke-width="2.5"/>
  <circle cx="-5" cy="-5" r="3.5" fill="#FFFDF7"/>
</g>`;

const createFirstthirdIcon = () => `<g opacity="0.85">
  <path d="M 0 -14 A 14 14 0 1 0 0 14 Q -6 0 0 -14" fill="#c8e3ff" stroke="#5a92d9" stroke-width="2"/>
  <circle cx="-4" cy="-4" r="2.5" fill="#FFFDF7"/>
  <path d="M 4 -8 L 5 -5 L 8 -5 L 6 -3 L 7 0 L 4 -1 L 1 0 L 2 -3 L 0 -5 L 3 -5 Z" fill="#7eb6ff" stroke="#5a92d9" stroke-width="0.5"/>
  <circle cx="6" cy="6" r="1.5" fill="#d1e7ff" opacity="0.7"/>
</g>`;

const createLastthirdIcon = () => `<g opacity="0.8">
  <path d="M 0 -12 A 12 12 0 1 0 0 12 Q -5 0 0 -12" fill="#8bb5e8" stroke="#2c3e50" stroke-width="1.5"/>
  <circle cx="-3" cy="-3" r="2" fill="#FFFDF7"/>
  <path d="M 6 -6 L 7 -3 L 10 -3 L 8 -1 L 9 2 L 6 1 L 3 2 L 4 -1 L 2 -3 L 5 -3 Z" fill="#FFD700" stroke="#FFA500" stroke-width="0.3"/>
</g>`;

const createDhuhrIcon = () => `<g>
  <circle cx="0" cy="0" r="16" fill="#FFD700" stroke="#DAA520" stroke-width="2"/>
  <g stroke="#FFD700" stroke-width="2.5">
    <line x1="0" y1="-22" x2="0" y2="-32"/>
    <line x1="0" y1="22" x2="0" y2="32"/>
    <line x1="-22" y1="0" x2="-32" y2="0"/>
    <line x1="22" y1="0" x2="32" y2="0"/>
  </g>
</g>`;

const createAsrIcon = () => `<g>
  <circle cx="0" cy="0" r="16" fill="#FFD700" stroke="#DAA520" stroke-width="2"/>
  <g stroke="#FFD700" stroke-width="2.5">
    <line x1="8" y1="8" x2="18" y2="18"/>
    <line x1="0" y1="16" x2="0" y2="28"/>
    <line x1="-8" y1="8" x2="-18" y2="18"/>
  </g>
</g>`;

const getPrayerStyleConfiguration = (prayerKey, isCurrentPrayer) => {
  const baseRadius = isCurrentPrayer ? 60 : 52;
  const configurations = {
    Sunrise: {
      circleFill: 'url(#sunrise-gradient)',
      circleStroke: '#ff980060',
      filter: 'url(#sunrise-glow)',
      icon: createSunIcon(),
      labelColor: '#E6B800',
      timeColor: '#E6B800',
      circleRadius: 40,
      borderWidth: 2.5
    },
    Fajr: {
      circleFill: 'url(#fajr-gradient)',
      circleStroke: '#3a6ea580',
      filter: 'url(#fajr-glow)',
      icon: createCrescentIcon(),
      labelColor: '#3a6ea5',
      timeColor: '#3a6ea5',
      circleRadius: baseRadius,
      borderWidth: 3.5
    },
    Isha: {
      circleFill: 'url(#fajr-gradient)',
      circleStroke: '#3a6ea580',
      filter: 'url(#fajr-glow)',
      icon: createCrescentIcon(),
      labelColor: '#3a6ea5',
      timeColor: '#3a6ea5',
      circleRadius: baseRadius,
      borderWidth: 3.5
    },
    Maghrib: {
      circleFill: 'url(#fajr-gradient)',
      circleStroke: '#3a6ea580',
      filter: 'url(#fajr-glow)',
      icon: createCrescentIcon(),
      labelColor: '#3a6ea5',
      timeColor: '#3a6ea5',
      circleRadius: baseRadius,
      borderWidth: 3.5
    },
    Firstthird: {
      circleFill: 'url(#firstthird-gradient)',
      circleStroke: '#5a92d960',
      filter: 'url(#firstthird-glow)',
      icon: createFirstthirdIcon(),
      labelColor: '#5a92d9',
      timeColor: '#5a92d9',
      circleRadius: 42,
      borderWidth: 2.5
    },
    Lastthird: {
      circleFill: 'url(#lastthird-gradient)',
      circleStroke: '#2c3e5060',
      filter: 'url(#lastthird-glow)',
      icon: createLastthirdIcon(),
      labelColor: '#4a5568',
      timeColor: '#4a5568',
      circleRadius: 40,
      borderWidth: 2.5
    },
    Dhuhr: {
      circleFill: 'url(#prayer-gradient)',
      circleStroke: '#ffd70080',
      filter: 'url(#prayer-glow)',
      icon: createDhuhrIcon(),
      labelColor: '#8B4513',
      timeColor: '#3D8B40',
      circleRadius: baseRadius,
      borderWidth: 3.5
    },
    Asr: {
      circleFill: 'url(#prayer-gradient)',
      circleStroke: '#ffd70080',
      filter: 'url(#prayer-glow)',
      icon: createAsrIcon(),
      labelColor: '#8B4513',
      timeColor: '#3D8B40',
      circleRadius: baseRadius,
      borderWidth: 3.5
    }
  };

  return configurations[prayerKey] || {
    circleFill: 'url(#prayer-gradient)',
    circleStroke: '#ffd70080',
    filter: 'url(#prayer-glow)',
    icon: createSunIcon(),
    labelColor: '#8B4513',
    timeColor: '#3D8B40',
    circleRadius: 40,
    borderWidth: 2.5
  };
};

const getPrayerPointStyle = (prayer, currentPrayer) => {
  if (!prayer?.key) return null;
  return getPrayerStyleConfiguration(prayer.key, currentPrayer === prayer.key);
};

const getLabelOffset = prayerKey => {
  const longLabels = ['Lastthird', 'Firstthird'];
  return longLabels.includes(prayerKey) ? 35 : 32; // Just 3px more for diacritics
};



const getLabelFontSize = prayerKey => {
  if (prayerKey === 'Sunrise') return '32';
  return '38'; // Keep original font size
};

const renderPrayerLabel = (containerGroup, prayer, positionX, positionY, circleRadius, labelColor) => {
  if (!containerGroup || !prayer || typeof positionX !== 'number' || typeof positionY !== 'number') return;

  const labelElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
  const labelOffset = getLabelOffset(prayer.key);

  labelElement.setAttribute('x', positionX);
  labelElement.setAttribute('y', positionY + circleRadius + labelOffset);
  labelElement.setAttribute('text-anchor', 'middle');
  labelElement.setAttribute('font-family', 'Reem Kufi, Amiri, serif');

  // Small adjustment for Arabic diacritics
  labelElement.setAttribute('dy', '0.2em');

  if (prayer.key === 'Sunrise') {
    labelElement.setAttribute('font-size', '32');
    labelElement.setAttribute('fill', '#E6B800');
    labelElement.setAttribute('font-weight', 'bold');
    labelElement.textContent = 'الشروق';
  } else {
    labelElement.setAttribute('font-size', getLabelFontSize(prayer.key));
    labelElement.setAttribute('fill', labelColor);
    labelElement.textContent = LOCALIZATION.prayerNames[prayer.key] || prayer.key;
  }

  containerGroup.appendChild(labelElement);
};

const getTimeOffset = prayerKey => {
  const longLabels = ['Lastthird', 'Firstthird'];
  return longLabels.includes(prayerKey) ? 73 : 70; // Just 3px more spacing
};

const renderPrayerTime = (containerGroup, prayer, times, positionX, positionY, circleRadius, timeColor) => {
  if (!containerGroup || !prayer || !times || typeof positionX !== 'number' || typeof positionY !== 'number') return;
  if (!times[prayer.key]) return;

  const timeElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
  const timeOffset = getTimeOffset(prayer.key);

  timeElement.setAttribute('x', positionX);
  timeElement.setAttribute('y', positionY + circleRadius + timeOffset);
  timeElement.setAttribute('text-anchor', 'middle');
  timeElement.setAttribute('font-size', '30');
  timeElement.setAttribute('font-family', 'Reem Kufi, Amiri, serif');
  timeElement.setAttribute('fill', timeColor);
  timeElement.textContent = typeof formatArabicTime === 'function' ?
    formatArabicTime(times[prayer.key]) : times[prayer.key];
  containerGroup.appendChild(timeElement);
};

const createOrbitElement = (centerX, centerY, radius, strokeColor, isUpperArc) => {
  const orbitPath = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
  orbitPath.setAttribute('cx', centerX);
  orbitPath.setAttribute('cy', centerY);
  orbitPath.setAttribute('r', radius);
  orbitPath.setAttribute('fill', 'none');
  orbitPath.setAttribute('stroke', strokeColor);
  orbitPath.setAttribute('stroke-width', '8');
  orbitPath.setAttribute('stroke-dasharray', '20,10');
  orbitPath.setAttribute('opacity', '0.4');
  
  return orbitPath;
};

const createConnectionLine = (centerX, centerY, prayerX, prayerY, strokeColor) => {
  const connectionLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
  connectionLine.setAttribute('x1', centerX);
  connectionLine.setAttribute('y1', centerY);
  connectionLine.setAttribute('x2', prayerX);
  connectionLine.setAttribute('y2', prayerY);
  connectionLine.setAttribute('stroke', strokeColor);
  connectionLine.setAttribute('stroke-width', '2');
  connectionLine.setAttribute('opacity', '0.3');
  connectionLine.setAttribute('stroke-dasharray', '5,5');
  return connectionLine;
};

const createPrayerCircle = (positionX, positionY, styleConfig) => {
  const circleElement = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
  circleElement.setAttribute('cx', positionX);
  circleElement.setAttribute('cy', positionY);
  circleElement.setAttribute('r', styleConfig.circleRadius);
  circleElement.setAttribute('fill', styleConfig.circleFill);
  circleElement.setAttribute('stroke', styleConfig.circleStroke);
  circleElement.setAttribute('stroke-width', styleConfig.borderWidth);
  if (styleConfig.filter !== 'none') circleElement.setAttribute('filter', styleConfig.filter);
  return circleElement;
};

const createPrayerIcon = (positionX, positionY, iconSVG) => {
  const iconGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
  iconGroup.setAttribute('transform', `translate(${positionX},${positionY - 7})`);
  iconGroup.innerHTML = iconSVG;
  return iconGroup;
};

const createCenterIndicator = (centerX, centerY) => {
  const centerDot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
  centerDot.setAttribute('cx', centerX);
  centerDot.setAttribute('cy', centerY);
  centerDot.setAttribute('r', '8');
  centerDot.setAttribute('fill', '#CD853F');
  centerDot.setAttribute('opacity', '0.8');
  return centerDot;
};

const calculatePrayerPosition = (angleDegrees, centerX, centerY, radius) => {
  // Maghrib at 0° (right side), Fajr at 180° (left side)
  // Night: 0° to 180° (right to left, top half)
  // Day: 180° to 360° (left to right, bottom half)
  const adjustedAngleRadians = (angleDegrees - TIMELINE_CONSTANTS.MAGHRIB_REFERENCE_OFFSET) * Math.PI / 180;
  return {
    x: centerX + radius * Math.cos(adjustedAngleRadians),
    y: centerY + radius * Math.sin(adjustedAngleRadians)
  };
};

const renderPrayerTimelineSVG = (prayers, times, currentPrayer) => {
  const svgElement = document.getElementById('prayer-timeline-svg');
  if (!svgElement || !prayers?.length || !times) return;
  
  svgElement.innerHTML = '';
  const centerX = TIMELINE_CONSTANTS.SVG_SIZE / 2;
  const centerY = TIMELINE_CONSTANTS.SVG_SIZE * TIMELINE_CONSTANTS.CENTER_Y_OFFSET;

  const definitionsElement = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  definitionsElement.innerHTML = createSVGDefinitions();
  svgElement.appendChild(definitionsElement);

  const fullOrbit = createOrbitElement(centerX, centerY, TIMELINE_CONSTANTS.ORBIT_RADIUS, '#CD853F', true);
  svgElement.appendChild(fullOrbit);

  const prayerAngles = getPrayerAngles(times);
    
  prayers.forEach((prayer, index) => {
    if (!prayer?.key || !times[prayer.key]) return;
    
    const angleDegrees = prayerAngles[index] || 0;
    const { x: prayerX, y: prayerY } = calculatePrayerPosition(angleDegrees, centerX, centerY, TIMELINE_CONSTANTS.ORBIT_RADIUS);
    
    const prayerGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    prayerGroup.setAttribute('class', `prayer-point-svg ${prayer.key.toLowerCase()}${currentPrayer === prayer.key ? ' current' : ''}`);

    const styleConfiguration = getPrayerPointStyle(prayer, currentPrayer);
    if (!styleConfiguration) return;

    const prayerCircle = createPrayerCircle(prayerX, prayerY, styleConfiguration);
    prayerGroup.appendChild(prayerCircle);

    if (styleConfiguration.icon) {
      const iconElement = createPrayerIcon(prayerX, prayerY, styleConfiguration.icon);
      prayerGroup.appendChild(iconElement);
    }

    renderPrayerLabel(prayerGroup, prayer, prayerX, prayerY, styleConfiguration.circleRadius, styleConfiguration.labelColor);
    renderPrayerTime(prayerGroup, prayer, times, prayerX, prayerY, styleConfiguration.circleRadius, styleConfiguration.timeColor);
    svgElement.appendChild(prayerGroup);
  });
};

const renderNextPrayerInfo = (nextPrayer, times, timeUntil) => {
  if (!nextPrayer || !times?.[nextPrayer] || !timeUntil) return;
  
  updateElementContent('.current-prayer-name', LOCALIZATION.prayerNames[nextPrayer] || nextPrayer);
  updateElementContent('.current-prayer-time', typeof formatArabicTime === 'function' ?
    formatArabicTime(times[nextPrayer]) : times[nextPrayer]);
  updateElementContent('.time-remaining', `<i class="fas fa-hourglass-half"></i> ${timeUntil} حتى ${LOCALIZATION.prayerNames[nextPrayer] || nextPrayer}`, true);
};

const headerStateManager = {
  currentState: 'normal',
  transitionInProgress: false,
  particleTimer: null,
  destroyed: false,

  iqamaDurations: {
    'Fajr': 25,
    'Dhuhr': 15,
    'Asr': 16,
    'Maghrib': 10,
    'Isha': 14  
  },

  prayerDurations: {
    'Fajr': 7,     
    'Dhuhr': 8,    
    'Asr': 6,      
    'Maghrib': 5,  
    'Isha': 9      
  },

  init() {
    if (this.destroyed) return;
    this.createBackgroundParticles();
    this.startParticleTimer();
    window.addEventListener('beforeunload', () => this.destroy());
  },

  destroy() {
    this.destroyed = true;
    this.clearParticleTimer();
    this.transitionInProgress = false;
    this.cleanupAllParticles();
  },

  cleanupAllParticles() {
    const particles = document.querySelectorAll('.header-bg-particle, .header-dissolve-particle');
    particles.forEach(particle => {
      if (particle.parentNode) {
        particle.parentNode.removeChild(particle);
      }
    });
  },

  clearParticleTimer() {
    if (this.particleTimer) {
      clearInterval(this.particleTimer);
      this.particleTimer = null;
    }
  },

  createBackgroundParticles() {
    const field = document.getElementById('headerParticleField');
    if (!field || this.destroyed) return;

    field.innerHTML = '';

    for (let i = 0; i < 15; i++) {
      if (this.destroyed) break;
      
      const particle = document.createElement('div');
      particle.classList.add('header-bg-particle');

      const size = Math.random() * 5 + 2;
      const left = Math.random() * 100;
      const delay = Math.random() * 6;
      const opacity = Math.random() * 0.4 + 0.2;

      particle.style.cssText = `
        width: ${size}px;
        height: ${size}px;
        left: ${left}%;
        animation-delay: ${delay}s;
        opacity: ${opacity};
      `;

      field.appendChild(particle);
    }
  },

  createScreenShake() {
    const container = document.querySelector('.current-prayer-status');
    if (!container) return;

    // Force animation restart to ensure shake effect works consistently
    container.style.animation = '';
    container.offsetHeight;
    container.style.animation = 'screenShake 0.6s ease-in-out';
    
    setTimeout(() => {
      if (container && !this.destroyed) {
        container.style.animation = '';
      }
    }, 600);
  },

  createFlashEffect(stateInfo) {
    if (this.destroyed) return;
    
    const flash = document.createElement('div');
    const colorMap = {
      'iqama': 'rgba(255, 68, 68, 0.8)',
      'prayer': 'rgba(76, 175, 80, 0.8)'
    };
    const flashColor = colorMap[stateInfo.state] || 'rgba(255, 215, 0, 0.8)';

    flash.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: ${flashColor};
      z-index: 9999;
      opacity: 0;
      pointer-events: none;
      animation: dramaticFlash 0.8s ease-out forwards;
    `;

    document.body.appendChild(flash);
    setTimeout(() => {
      if (flash.parentNode && !this.destroyed) {
        flash.parentNode.removeChild(flash);
      }
    }, 800);
  },

  startParticleTimer() {
    this.clearParticleTimer();
    if (this.destroyed) return;
    
    this.particleTimer = setInterval(() => {
      if (!this.destroyed) {
        this.createBackgroundParticles();
      }
    }, 8000);
  },

  createTransitionParticles(stateInfo) {
    const container = document.querySelector('.current-prayer-status');
    if (!container || this.destroyed) return;

    this.addPrimaryParticles(container, stateInfo);
    this.scheduleDelayedParticles(container, stateInfo);
    
    if (stateInfo.state !== 'normal') {
      this.addCrackEffects(container);
    }
  },

  addPrimaryParticles(container, stateInfo) {
    if (this.destroyed) return;
    
    const particleClass = this.getParticleClass(stateInfo.state);
    const particleCount = stateInfo.state === 'normal' ? 20 : 35;

    for (let i = 0; i < particleCount && !this.destroyed; i++) {
      const particle = this.buildParticle(particleClass);
      container.appendChild(particle);
      this.scheduleParticleRemoval(particle, 3500);
    }
  },

  scheduleDelayedParticles(container, stateInfo) {
    if (this.destroyed) return;
    
    setTimeout(() => {
      if (this.destroyed) return;
      
      const particleClass = this.getParticleClass(stateInfo.state);
      for (let i = 0; i < 10 && !this.destroyed; i++) {
        const particle = this.buildSecondaryParticle(particleClass);
        container.appendChild(particle);
        this.scheduleParticleRemoval(particle, 3000);
      }
    }, 400);
  },

  buildParticle(particleClass) {
    const particle = document.createElement('div');
    particle.classList.add('header-dissolve-particle', particleClass);
    
    const size = Math.random() * 12 + 4;
    const tx = (Math.random() - 0.5) * 400;
    const ty = (Math.random() - 0.5) * 400;
    const delay = Math.random() * 1.2;

    particle.style.cssText = `
      --tx: ${tx}px;
      --ty: ${ty}px;
      width: ${size}px;
      height: ${size}px;
      top: ${Math.random() * 100}%;
      left: ${Math.random() * 100}%;
      animation-delay: ${delay}s;
    `;
    return particle;
  },

  buildSecondaryParticle(particleClass) {
    const particle = document.createElement('div');
    particle.classList.add('header-dissolve-particle', particleClass);
    
    const size = Math.random() * 6 + 2;
    const tx = (Math.random() - 0.5) * 300;
    const ty = (Math.random() - 0.5) * 300;

    particle.style.cssText = `
      --tx: ${tx}px;
      --ty: ${ty}px;
      width: ${size}px;
      height: ${size}px;
      top: ${Math.random() * 100}%;
      left: ${Math.random() * 100}%;
      animation-delay: 0s;
    `;
    return particle;
  },

  addCrackEffects(container) {
    if (this.destroyed) return;
    this.addMainCracks(container);
    this.addLightningEffects(container);
  },

  addMainCracks(container) {
    if (this.destroyed) return;
    
    for (let i = 0; i < 12 && !this.destroyed; i++) {
      const crack = this.buildCrack();
      container.appendChild(crack);
      this.scheduleParticleRemoval(crack, 4500);
    }
  },

  addLightningEffects(container) {
    if (this.destroyed) return;
    
    for (let i = 0; i < 3 && !this.destroyed; i++) {
      const lightning = this.buildLightning();
      container.appendChild(lightning);
      this.scheduleParticleRemoval(lightning, 2500);
    }
  },

  buildCrack() {
    const crack = document.createElement('div');
    const size = Math.random() * 25 + 15;
    const intensity = Math.random() * 0.4 + 0.6;

    crack.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      top: ${Math.random() * 100}%;
      left: ${Math.random() * 100}%;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(255, 255, 255, ${intensity}), transparent);
      box-shadow: 0 0 25px rgba(255, 255, 255, 0.8), 0 0 50px rgba(255, 255, 255, 0.4);
      z-index: 8;
      opacity: 0;
      animation: headerCrackEffect 2.5s forwards;
      animation-delay: ${Math.random() * 2}s;
    `;
    return crack;
  },

  buildLightning() {
    const lightning = document.createElement('div');
    lightning.style.cssText = `
      position: absolute;
      width: 2px;
      height: ${Math.random() * 40 + 20}px;
      top: ${Math.random() * 80 + 10}%;
      left: ${Math.random() * 80 + 10}%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
      z-index: 9;
      opacity: 0;
      transform: rotate(${Math.random() * 60 - 30}deg);
      animation: lightningFlash 1.5s forwards;
      animation-delay: ${Math.random() * 1}s;
    `;
    return lightning;
  },

  getParticleClass(state) {
    return {
      'iqama': 'particle-red',
      'prayer': 'particle-green'
    }[state] || 'particle-gold';
  },

  scheduleParticleRemoval(particle, delay) {
    if (!particle || this.destroyed) return;
    
    setTimeout(() => {
      if (particle && particle.parentNode && !this.destroyed) {
        try {
          particle.parentNode.removeChild(particle);
        } catch (e) {
          // Silently ignore if element already removed
        }
      }
    }, delay);
  },

  getCurrentState(currentMinutes, prayerTimes) {
    if (typeof currentMinutes !== 'number' || !prayerTimes) {
      return { state: 'normal' };
    }

    const prayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];

    for (const prayer of prayers) {
      if (!prayerTimes[prayer]) continue;

      let prayerMinutes;
      if (typeof timeToMinutes === 'function') {
        prayerMinutes = timeToMinutes(prayerTimes[prayer]);
        if (prayerMinutes === null || prayerMinutes === undefined) continue;
      } else {
        prayerMinutes = this.parseTimeString(prayerTimes[prayer]);
        if (prayerMinutes === null) continue;
      }

      const state = this.calculatePrayerState(currentMinutes, prayerMinutes, prayer);
      if (state) return state;
    }

    return { state: 'normal' };
  },

  parseTimeString(timeString) {
    if (!timeString) return null;
    
    const timeParts = String(timeString).trim().split(':');
    if (timeParts.length !== 2) return null;
    
    const hours = parseInt(timeParts[0], 10);
    const minutes = parseInt(timeParts[1], 10);
    
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours >= 24 || minutes < 0 || minutes >= 60) {
      return null;
    }
    
    return hours * 60 + minutes;
  },

  calculatePrayerState(currentMinutes, prayerMinutes, prayer) {
    const iqamaDuration = this.iqamaDurations[prayer] || 10;
    const prayerDuration = this.prayerDurations[prayer] || 5;
    const iqamaEndMinutes = prayerMinutes + iqamaDuration;
    const prayerEndMinutes = iqamaEndMinutes + prayerDuration;

    if (this.isTimeInRange(currentMinutes, prayerMinutes, iqamaEndMinutes)) {
      return {
        state: 'iqama',
        prayer: prayer,
        timeRemaining: this.calculateTimeDifference(currentMinutes, iqamaEndMinutes)
      };
    }

    if (this.isTimeInRange(currentMinutes, iqamaEndMinutes, prayerEndMinutes)) {
      return {
        state: 'prayer',
        prayer: prayer,
        timeRemaining: this.calculateTimeDifference(currentMinutes, prayerEndMinutes)
      };
    }

    return null;
  },

  isTimeInRange(current, start, end) {
    // Handle midnight boundary crossover
    if (end > 1440) {
      return current >= start || current < (end - 1440);
    }
    return current >= start && current < end;
  },

  calculateTimeDifference(current, target) {
    let diff = target - current;
    // Ensure positive difference for next day calculations
    if (diff <= 0) {
      diff += 1440;
    }
    return diff;
  },

  async updateHeaderState(currentMinutes, prayerTimes, nextPrayer, timeUntil) {
    if (this.destroyed) return;
    
    try {
      const stateInfo = this.getCurrentState(currentMinutes, prayerTimes);

      if (this.shouldTransition(stateInfo)) {
        await this.transitionToState(stateInfo, prayerTimes, nextPrayer, timeUntil);
      } else if (!this.transitionInProgress) {
        this.updateStateContent(stateInfo, prayerTimes, nextPrayer, timeUntil);
      }
    } catch (error) {
      console.error('Header state update failed:', error);
      this.currentState = 'normal';
      this.transitionInProgress = false;
    }
  },

  shouldTransition(stateInfo) {
    return stateInfo.state !== this.currentState && !this.transitionInProgress;
  },

  async transitionToState(stateInfo, prayerTimes, nextPrayer, timeUntil) {
    if (this.destroyed) return;
    
    this.transitionInProgress = true;
    const statusElement = document.querySelector('.current-prayer-status');

    if (!statusElement) {
      this.transitionInProgress = false;
      return;
    }

    try {
      statusElement.classList.add('transitioning');
      this.applyStateEffects(stateInfo, statusElement);
      await this.performTransition(statusElement, stateInfo, prayerTimes, nextPrayer, timeUntil);
      this.currentState = stateInfo.state;
    } catch (error) {
      console.error('State transition failed:', error);
    } finally {
      if (!this.destroyed) {
        this.transitionInProgress = false;
        if (statusElement) {
          statusElement.classList.remove('transitioning');
        }
      }
    }
  },

  applyStateEffects(stateInfo, statusElement) {
    if (this.destroyed) return;
    
    if (stateInfo.state !== 'normal') {
      this.createScreenShake();
      this.createFlashEffect(stateInfo);
    }
    this.createTransitionParticles(stateInfo);
  },

  async performTransition(statusElement, stateInfo, prayerTimes, nextPrayer, timeUntil) {
    if (this.destroyed) return;
    
    statusElement.classList.add('fade-transition');
    await this.delay(800);

    if (this.destroyed) return;

    this.resetStateClasses(statusElement);
    this.updateStateContent(stateInfo, prayerTimes, nextPrayer, timeUntil);
    this.applyStateClass(statusElement, stateInfo);

    statusElement.classList.remove('fade-transition');
    await this.delay(2000);
  },

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  resetStateClasses(statusElement) {
    statusElement.classList.remove('iqama-state', 'prayer-state');
  },

  applyStateClass(statusElement, stateInfo) {
    if (stateInfo.state === 'iqama') {
      statusElement.classList.add('iqama-state');
    } else if (stateInfo.state === 'prayer') {
      statusElement.classList.add('prayer-state');
    }
  },

  updateStateContent(stateInfo, prayerTimes, nextPrayer, timeUntil) {
    if (this.destroyed) return;
    
    const elements = this.getStateElements();
    if (!this.validateStateElements(elements)) return;

    const stateHandlers = {
      'iqama': () => this.updateIqamaState(elements, stateInfo),
      'prayer': () => this.updatePrayerState(elements, stateInfo),
      'normal': () => this.updateNormalState(elements, prayerTimes, nextPrayer, timeUntil)
    };

    const handler = stateHandlers[stateInfo.state] || stateHandlers['normal'];
    handler();
  },

  getStateElements() {
    return {
      label: document.querySelector('.current-prayer-label'),
      name: document.querySelector('.current-prayer-name'),
      time: document.querySelector('.current-prayer-time'),
      remaining: document.querySelector('.time-remaining')
    };
  },

  validateStateElements(elements) {
    const missingElements = Object.keys(elements).filter(key => !elements[key]);
    if (missingElements.length > 0) {

      return false;
    }
    return true;
  },

  updateIqamaState(elements, stateInfo) {
    elements.label.innerHTML = '<i class="fas fa-bell"></i> موعد الإقامة';
    elements.name.textContent = 'الله أكبر';
    elements.time.textContent = 'استعدوا للصلاة';
    elements.remaining.innerHTML = `<i class="fas fa-clock"></i> ${this.formatTimeWithSeconds(stateInfo.timeRemaining)} للإقامة`;
  },

  updatePrayerState(elements, stateInfo) {
    elements.label.innerHTML = '<i class="fas fa-pray"></i> الصلاة قائمة';
    elements.name.innerHTML = 'صلاة <span class="prayer-hands-icon">🤲</span>';
    elements.time.textContent = 'الصلاة قائمة...';
    
    if (stateInfo.timeRemaining) {
      elements.remaining.innerHTML = `<i class="fas fa-clock"></i> ${this.formatTimeWithSeconds(stateInfo.timeRemaining)} متبقية`;
    } else {
      elements.remaining.innerHTML = '<i class="fas fa-heart"></i> الحمد لله';
    }
  },

  updateNormalState(elements, prayerTimes, nextPrayer, timeUntil) {
    if (!nextPrayer || !prayerTimes?.[nextPrayer] || !timeUntil) return;
    
    elements.label.innerHTML = '<i class="fas fa-clock"></i> الصلاة الحالية';
    elements.name.textContent = LOCALIZATION.prayerNames[nextPrayer] || nextPrayer;
    elements.time.textContent = typeof formatArabicTime === 'function' ?
      formatArabicTime(prayerTimes[nextPrayer]) : prayerTimes[nextPrayer];
    elements.remaining.innerHTML = `<i class="fas fa-hourglass-half"></i> ${timeUntil} حتى ${LOCALIZATION.prayerNames[nextPrayer] || nextPrayer}`;
  },

  formatTimeWithSeconds(totalMinutes) {
    if (typeof totalMinutes !== 'number' || totalMinutes < 0) return '0 ثانية';
    
    const hours = Math.floor(totalMinutes / 60);
    const minutes = Math.floor(totalMinutes % 60);
    const now = new Date();
    const seconds = Math.max(0, 60 - now.getSeconds());

    const formatNumber = (num) => {
      return typeof toArabicNumerals === 'function' ? toArabicNumerals(num) : String(num);
    };

    const parts = [];
    if (hours > 0) parts.push(`${formatNumber(hours)} ساعة`);
    if (minutes > 0) parts.push(`${formatNumber(minutes)} دقيقة`);
    if (seconds > 0 || parts.length === 0) parts.push(`${formatNumber(seconds)} ثانية`);

    return parts.join(' و');
  }
};

const contentManager = {
  elements: {},
  state: {
    timer: null,
    secondsRemaining: 180,
    currentType: null,
    preloadedContent: null,
    lastFontSize: null
  },
  
  config: {
    timerDuration: 180,
    preloadTrigger: 30,
    maxTextLength: 400,
    verseChance: 0.6,
    hardcodedChance: 0.6,
    fontSizes: {
      arabic: { min: 10, max: 25 },
      other: { min: 8, max: 24 }
    }
  },

  async init() {
    this.initializeElements();
    await this.loadInitialContent();
    this.displayRandomContent();
    this.startTimer();
  },

  initializeElements() {
    this.elements = {
      title: document.getElementById('content-title'),
      text: document.getElementById('content-text'),
      source: document.getElementById('content-source'),
      section: document.getElementById('content-section')
    };
  },

  async loadInitialContent() {
    try {
      const contentType = this.selectContentType();
      await this.loadContent(contentType);
      
      const oppositeType = contentType === 'verse' ? 'hadith' : 'verse';
      this.loadContent(oppositeType).catch(() => {});
    } catch (error) {
      console.error('Failed to load initial content:', error);
    }
  },

  selectContentType() {
    return Math.random() < this.config.verseChance ? 'verse' : 'hadith';
  },

  displayRandomContent() {
    const contentType = this.state.preloadedContent?.type ?? this.selectContentType();
    this.showContent(contentType);
    this.preloadOppositeContent(contentType);
  },

  preloadOppositeContent(currentType) {
    const oppositeType = currentType === 'verse' ? 'hadith' : 'verse';
    setTimeout(() => this.loadContent(oppositeType).catch(() => {}), 1000);
  },

  showContent(type) {
    const titles = { verse: 'آية قرآنية', hadith: 'حديث شريف' };
    this.elements.title.textContent = titles[type];
    this.animateTransition(type);
    this.state.currentType = type;
  },

  animateTransition(type) {
    this.elements.section.classList.add('fade-out');
    
    setTimeout(async () => {
      try {
        if (this.state.preloadedContent?.type === type) {
          this.updateContentElements(this.state.preloadedContent);
          this.state.preloadedContent = null;
        } else {
          await this.loadAndDisplayContent(type);
        }
      } finally {
        this.elements.section.classList.remove('fade-out');
      }
    }, 300);
  },

  async loadAndDisplayContent(type) {
    try {
      const content = await this.loadContentWithFallback(type);
      this.updateContentElements(content);
    } catch (error) {
      console.error(`Failed to load ${type} content:`, error);
    }
  },

  async loadContent(type) {
    const content = await this.loadContentWithFallback(type);
    this.handleLoadedContent(content, type);
    return content;
  },

  async loadContentWithFallback(type) {
    const useHardcoded = Math.random() < this.config.hardcodedChance;
    
    if (useHardcoded) {
      return this.getHardcodedContent(type);
    }
    
    try {
      return await this.fetchContentFromAPI(type);
    } catch (error) {

      return this.getHardcodedContent(type);
    }
  },

  handleLoadedContent(content, type) {
    if (type === this.state.currentType) {
      this.updateContentElements(content);
    } else {
      this.state.preloadedContent = { ...content, type };
    }
  },

  getHardcodedContent(type) {
    const collection = CONTENT_DATA[`${type}s`];
    if (!collection?.length) {
      throw new Error(`No ${type} data available`);
    }
    
    const randomIndex = Math.floor(Math.random() * collection.length);
    return collection[randomIndex];
  },

  async fetchContentFromAPI(type) {
    return type === 'verse' ? this.fetchVerseFromAPI() : this.fetchHadithFromAPI();
  },

  async fetchVerseFromAPI() {
    const surahNumber = Math.floor(Math.random() * 114) + 1;
    const surahData = await this.fetchSurahData(surahNumber);
    const verse = await this.fetchRandomVerse(surahData);

    return {
      text: verse.text,
      source: `${verse.surah.name} - الآية ${toArabicNumerals(verse.numberInSurah)}`
    };
  },

  async fetchSurahData(surahNumber) {
    const response = await fetch(`https://api.alquran.cloud/v1/surah/${surahNumber}/ar.alafasy`);
    
    if (!response.ok) {
      throw new Error(`Surah API error: ${response.status}`);
    }
    
    const data = await response.json();
    if (data.code !== 200) {
      throw new Error('Invalid surah response');
    }
    
    return data.data;
  },

  async fetchRandomVerse(surah) {
    const ayahNumber = Math.floor(Math.random() * surah.numberOfAyahs) + 1;
    const response = await fetch(`https://api.alquran.cloud/v1/ayah/${surah.number}:${ayahNumber}/ar.alafasy`);
    
    if (!response.ok) {
      throw new Error(`Ayah API error: ${response.status}`);
    }
    
    const data = await response.json();
    if (data.code !== 200) {
      throw new Error('Invalid ayah response');
    }
    
    return data.data;
  },

  async fetchHadithFromAPI() {
    const collections = Object.keys(COLLECTIONS);
    const collection = collections[Math.floor(Math.random() * collections.length)];
    const response = await fetch(`https://cdn.jsdelivr.net/gh/fawazahmed0/hadith-api@1/editions/ara-${collection}.json`);
    
    if (!response.ok) {
      throw new Error(`Hadith API error: ${response.status}`);
    }
    
    const data = await response.json();
    if (!data.hadiths?.length) {
      throw new Error('No hadiths in response');
    }

    const hadith = data.hadiths[Math.floor(Math.random() * data.hadiths.length)];
    return {
      text: hadith.text,
      source: `${COLLECTIONS[collection]} - الحديث رقم ${toArabicNumerals(hadith.hadithnumber || 1)}`
    };
  },

  updateContentElements(content) {
    this.resetTextElement();
    
    if (content.text.length > this.config.maxTextLength) {
      this.truncateText(this.elements.text, content.text);
    } else {
      this.elements.text.textContent = content.text;
      this.adjustFontSize(this.elements.text);
    }
    
    this.elements.source.textContent = content.source;
  },

  resetTextElement() {
    this.elements.text.style.fontSize = '';
    this.elements.text.classList.remove('truncated');
    this.state.lastFontSize = null;
  },

  truncateText(element, text) {
    const isArabic = /[\u0600-\u06FF]/.test(text);
    if (isArabic) {
      element.style.direction = 'rtl';
      element.style.textAlign = 'right';
    }

    const truncated = this.getTruncatedText(text);
    element.textContent = truncated + ' ...';
    element.classList.add('truncated');
    element.title = text;
    this.adjustFontSize(element);
  },

  getTruncatedText(text) {
    const sentenceTruncated = this.truncateBySentences(text);
    const minLength = this.config.maxTextLength * 0.7;
    
    return sentenceTruncated.length >= minLength 
      ? sentenceTruncated 
      : this.truncateByWords(text);
  },

  truncateBySentences(text) {
    const sentences = text.split(/([.!؟۔﴾﴿]|\d+:\d+)/);
    let result = '';
    const maxLength = this.config.maxTextLength - 20;

    for (let i = 0; i < sentences.length; i += 2) {
      const sentence = (sentences[i] || '') + (sentences[i + 1] || '');
      if ((result + sentence).length > maxLength) break;
      result += sentence;
    }

    return result.trim();
  },

  truncateByWords(text) {
    const words = text.split(' ');
    let result = '';
    const maxLength = this.config.maxTextLength;

    for (const word of words) {
      const testLength = (result + word + ' ...').length;
      if (testLength > maxLength) break;
      result += word + ' ';
    }

    return result.trim() || text.substring(0, maxLength - 10);
  },

  adjustFontSize(element) {
    const container = element.parentElement;
    if (!container) return;
    
    const { clientHeight: maxHeight, clientWidth: maxWidth } = container;
    if (!maxHeight || !maxWidth) return;

    const isArabic = /[\u0600-\u06FF]/.test(element.textContent);
    const { min, max } = this.config.fontSizes[isArabic ? 'arabic' : 'other'];
    
    // Use cached font size as starting point for performance
    let optimalSize = this.state.lastFontSize || min;
    let testMin = min;
    let testMax = max;

    while (testMin <= testMax) {
      const fontSize = Math.floor((testMin + testMax) / 2);
      element.style.fontSize = fontSize + 'px';

      const fitsContainer = element.scrollHeight <= maxHeight && 
                           element.scrollWidth <= maxWidth;

      if (fitsContainer) {
        optimalSize = fontSize;
        testMin = fontSize + 1;
      } else {
        testMax = fontSize - 1;
      }
    }

    element.style.fontSize = optimalSize + 'px';
    this.state.lastFontSize = optimalSize;
  },

  startTimer() {
    this.clearTimer();
    this.state.secondsRemaining = this.config.timerDuration;

    this.state.timer = setInterval(() => {
      this.state.secondsRemaining--;

      if (this.state.secondsRemaining === this.config.preloadTrigger) {
        this.preloadNextContent();
      }

      if (this.state.secondsRemaining <= 0) {
        this.handleTimerExpiry();
      }
    }, 1000);
  },

  clearTimer() {
    if (this.state.timer) {
      clearInterval(this.state.timer);
      this.state.timer = null;
    }
  },

  preloadNextContent() {
    const nextType = this.selectContentType();
    this.loadContent(nextType).catch(() => {});
  },

  handleTimerExpiry() {
    this.displayRandomContent();
    this.state.secondsRemaining = this.config.timerDuration;
  },

  destroy() {
    this.clearTimer();
    this.state.preloadedContent = null;
    this.elements = {};
  }
};

const renderLocationInfo = locationName => {
  updateElementContent('.location-name', `<i class="fas fa-location-dot"></i> ${locationName}`, true);
};

const decorativeBorderManager = {
  svgContent: `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="92 50 200 200">
  <defs>
    <style>
      .main-shape {
        fill: #8B4513;
        stroke: #2C5530;
        stroke-width: 1.5;
        transform-box: fill-box; 
        transform-origin: 50% 50%;
      }
      .detail-circle {
        fill: #DAA520;
        stroke: #DAA520;
        stroke-width: 0.1;
      }
    </style>
  </defs>
</svg>`,

  init() {
    const containers = this.getContainers();
    const corners = this.getCorners();

    if (!containers.top) return;

    const svgDoc = this.parseSVGContent();
    this.populateCorners(corners);
    this.populateContainers(containers, svgDoc);
    this.setupResizeHandler(containers, svgDoc);
  },

  getContainers() {
    return {
      top: document.querySelector('.decorative-border.top'),
      right: document.querySelector('.decorative-border.right'),
      bottom: document.querySelector('.decorative-border.bottom'),
      left: document.querySelector('.decorative-border.left')
    };
  },

  getCorners() {
    return {
      topLeft: document.querySelector('.corner-decoration.top-left'),
      topRight: document.querySelector('.corner-decoration.top-right'),
      bottomLeft: document.querySelector('.corner-decoration.bottom-left'),
      bottomRight: document.querySelector('.corner-decoration.bottom-right')
    };
  },

  parseSVGContent() {
    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(this.svgContent, 'image/svg+xml');
    
    // Check for parsing errors
    const parserError = svgDoc.querySelector('parsererror');
    if (parserError) {
      throw new Error('Failed to parse SVG content');
    }
    
    return svgDoc;
  },

  populateCorners(corners) {
    const cornerSvgContent = this.createCornerSVGContent();
    const parser = new DOMParser();
    const cornerSvgDoc = parser.parseFromString(cornerSvgContent, 'image/svg+xml');

    const parserError = cornerSvgDoc.querySelector('parsererror');
    if (parserError) {
      throw new Error('Failed to parse corner SVG content');
    }

    Object.values(corners).forEach(corner => {
      if (corner) {
        const cornerSvg = cornerSvgDoc.documentElement.cloneNode(true);
        corner.appendChild(cornerSvg);
      }
    });
  },

  createCornerSVGContent() {
    return `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="92 50 200 200">
  <defs>
    <style>
      .main-shape {
        fill: #8B4513;
        stroke: #2C5530;
        stroke-width: 1.5;
        transform-box: fill-box; 
        transform-origin: 50% 50%;
      }
      .detail-circle {
        fill: #DAA520;
        stroke: #DAA520;
        stroke-width: 0.1;
      }
    </style>
  </defs>
</svg>`;
  },

  populateContainers(containers, svgDoc) {
    Object.keys(containers).forEach(side => {
      const container = containers[side];
      if (!container) return;

      container.innerHTML = '';
      const repeatCount = this.calculateRepeatCount(container, 40);

      for (let i = 0; i < repeatCount; i++) {
        const clone = svgDoc.documentElement.cloneNode(true);
        container.appendChild(clone);
      }
    });
  },

  calculateRepeatCount(container, svgSize) {
    const containerRect = container.getBoundingClientRect();
    
    const isHorizontal = container.classList.contains('top') || container.classList.contains('bottom');
    const availableSpace = isHorizontal ? containerRect.width : containerRect.height;
    
    if (availableSpace <= 0) return 1;
    
    const effectiveSvgSize = svgSize * (isHorizontal ? 0.99 : 0.92);

    return Math.max(1, Math.ceil(availableSpace / effectiveSvgSize));
  },

  setupResizeHandler(containers, svgDoc) {
    let resizeTimeout;
    
    const handleResize = () => {
      Object.keys(containers).forEach(side => {
        const container = containers[side];
        if (!container) return;

        const currentCount = container.children.length;
        const neededCount = this.calculateRepeatCount(container, 40);

        this.adjustContainerSVGCount(container, currentCount, neededCount, svgDoc);
      });
    };

    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(handleResize, 100);
    });
  },

  adjustContainerSVGCount(container, currentCount, neededCount, svgDoc) {
    if (neededCount > currentCount) {
      for (let i = currentCount; i < neededCount; i++) {
        const clone = svgDoc.documentElement.cloneNode(true);
        container.appendChild(clone);
      }
    } else if (neededCount < currentCount) {
      for (let i = currentCount - 1; i >= neededCount; i--) {
        if (container.children[i]) {
          container.removeChild(container.children[i]);
        }
      }
    }
  }
};

const ReverseGeocodeCache = (() => {
  const cache = new Map();
  const LIFETIME = 1800000;
  const MAX_SIZE = 30;
  
  setInterval(() => {
    const now = Date.now();
    for (const [key, entry] of cache) {
      if (now - entry.timestamp > LIFETIME) {
        cache.delete(key);
      }
    }
  }, 600000);
  
  return {
    get: (key) => {
      const entry = cache.get(key);
      if (entry && Date.now() - entry.timestamp < LIFETIME) {
        return entry.data;
      }
      cache.delete(key);
      return null;
    },
    set: (key, data) => {
      if (cache.size >= MAX_SIZE) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }
      cache.set(key, { data, timestamp: Date.now() });
    }
  };
})();

const TRANSLATIONS = {
  cities: {
    'Sidon': 'صيدا', 'Beirut': 'بيروت', 'Tripoli': 'طرابلس', 'Tyre': 'صور',
    'Baalbek': 'بعلبك', 'Jounieh': 'جونيه', 'Byblos': 'جبيل', 'Zahle': 'زحلة'
  },
  countries: {
    'Lebanon': 'لبنان', 'Syria': 'سوريا', 'Palestine': 'فلسطين', 'Israel': 'فلسطين المحتلة'
  }
};

class LocationCache {
  static result = null;
  static timestamp = null;
  static coordsHistory = [];
  static isProcessing = false;
  static processStartTime = null;
  static userPreferences = {
    preferredMethod: null,
    lastSuccessfulMethod: null,
    methodSuccessRates: {}
  };

  static set(result) {
    this.result = result;
    this.timestamp = Date.now();

    // Track successful method
    if (result.method) {
      this.userPreferences.lastSuccessfulMethod = result.method;
      this.userPreferences.methodSuccessRates[result.method] =
        (this.userPreferences.methodSuccessRates[result.method] || 0) + 1;
    }

    if (result.coords && this._isValidCoord(result.coords)) {
      const coordEntry = {
        coords: {
          lat: result.coords.lat,
          lng: result.coords.lng,
          accuracy: result.coords.accuracy > 0 ? result.coords.accuracy : null
        },
        timestamp: Date.now(),
        method: result.method
      };

      const fiveMinutesAgo = Date.now() - 300000;
      this.coordsHistory = this.coordsHistory
        .filter(entry => entry.timestamp > fiveMinutesAgo)
        .slice(-9);

      this.coordsHistory.push(coordEntry);
    }
  }

  static get(maxAge = 300000) {
    return this.result && this.timestamp && 
           Date.now() - this.timestamp <= maxAge ? this.result : null;
  }

  static hasExpired(maxAge = 1800000) {
    return !this.timestamp || Date.now() - this.timestamp > maxAge;
  }

  static _isValidCoord(coords) {
    return coords && 
           typeof coords.lat === 'number' && 
           typeof coords.lng === 'number' &&
           Number.isFinite(coords.lat) && 
           Number.isFinite(coords.lng) &&
           Math.abs(coords.lat) <= 90 &&
           Math.abs(coords.lng) <= 180;
  }

  static getStableCoords(newCoords) {
    if (this.coordsHistory.length < 2) return newCoords;
    
    const recentCoords = this.coordsHistory.slice(-5);
    const threshold = newCoords.accuracy <= 20 ? 25 : Math.max(50, (newCoords.accuracy || 50) * 1.2);
    
    let latSum = 0, lngSum = 0, validCount = 0;
    const validCoords = [];
    
    for (const h of recentCoords) {
      if (this._isValidCoord(h.coords)) {
        latSum += h.coords.lat;
        lngSum += h.coords.lng;
        validCount++;
        validCoords.push(h.coords);
      }
    }
    
    if (validCount === 0) return newCoords;
    
    const centroid = { lat: latSum / validCount, lng: lngSum / validCount };
    const clusteredCoords = [];
    
    for (const coord of validCoords) {
      try {
        if (this.calculateDistance(coord, centroid) < threshold) {
          clusteredCoords.push(coord);
        }
      } catch (error) {
        continue;
      }
    }

    if (clusteredCoords.length >= 2) {
      const allCoords = [...clusteredCoords, newCoords];
      let totalLat = 0, totalLng = 0, totalWeight = 0;
      
      for (const coord of allCoords) {
        const weight = coord.accuracy ? 1 / Math.max(coord.accuracy, 10) : 1;
        totalLat += coord.lat * weight;
        totalLng += coord.lng * weight;
        totalWeight += weight;
      }
      
      return {
        lat: +(totalLat / totalWeight).toFixed(6),
        lng: +(totalLng / totalWeight).toFixed(6),
        accuracy: Math.min(...allCoords.map(c => c.accuracy || 1000).filter(a => a < 1000)) || null
      };
    }
    
    return newCoords;
  }

  static calculateDistance(coords1, coords2) {
    if (!this._isValidCoord(coords1) || !this._isValidCoord(coords2)) {
      throw new Error('Invalid coordinates for distance calculation');
    }

    const R = 6371e3;
    const toRad = Math.PI / 180;
    const φ1 = coords1.lat * toRad;
    const φ2 = coords2.lat * toRad;
    const Δφ = (coords2.lat - coords1.lat) * toRad;
    const Δλ = (coords2.lng - coords1.lng) * toRad;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    
    return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  }

  static startProcessing() {
    this.isProcessing = true;
    this.processStartTime = Date.now();
  }

  static stopProcessing() {
    this.isProcessing = false;
    this.processStartTime = null;
  }

  static isProcessingTooLong(maxTime = 10000) {
    return this.isProcessing && 
           this.processStartTime && 
           Date.now() - this.processStartTime > maxTime;
  }
}

class GeolocationError extends Error {
  constructor(message, type, attempts = {}) {
    super(message);
    this.name = 'GeolocationError';
    this.type = type;
    this.attempts = attempts;
  }
}

const getPosition = (options, timeout = 6000) => {
  return new Promise((resolve, reject) => {
    let resolved = false;
    let watchId = null;
    let bestPosition = null;
    let positionCount = 0;
    let timeoutId = null;
    let firstPositionReceived = false;
    
    const cleanup = () => {
      if (watchId !== null) {
        try {
          navigator.geolocation.clearWatch(watchId);
        } catch (e) {}
        watchId = null;
      }
      if (timeoutId !== null) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };

    const resolveOnce = (result) => {
      if (!resolved) {
        resolved = true;
        cleanup();
        resolve(result);
      }
    };

    const rejectOnce = (error) => {
      if (!resolved) {
        resolved = true;
        cleanup();
        reject(error);
      }
    };

    timeoutId = setTimeout(() => {
      if (bestPosition) {
        resolveOnce(bestPosition);
      } else {
        rejectOnce(new Error(`Timeout: ${timeout}ms`));
      }
    }, timeout);

    const successHandler = (position) => {
      if (resolved) return;
      
      positionCount++;
      const accuracy = position.coords.accuracy || 1000;
      
      if (!bestPosition || accuracy < bestPosition.coords.accuracy) {
        bestPosition = position;
      }
      
      // Return quickly for decent accuracy or after reasonable attempts
      if (!firstPositionReceived) {
        firstPositionReceived = true;
        // For first position, be more lenient with accuracy requirements
        if (accuracy <= 100) {
          resolveOnce(bestPosition);
          return;
        }
      }
      
      if (accuracy <= 50 || positionCount >= 3) {
        resolveOnce(bestPosition);
      }
    };

    const errorHandler = (error) => {
      const messages = {
        1: 'Location access denied',
        2: 'Location unavailable', 
        3: 'Location timeout'
      };
      rejectOnce(new Error(messages[error.code] || `Geolocation error: ${error.message}`));
    };

    try {
      watchId = navigator.geolocation.watchPosition(successHandler, errorHandler, options);
    } catch (error) {
      rejectOnce(error);
    }
  });
};

const getFastLocation = async () => {
  // Fast track: Return first successful result for immediate UI response
  const fastMethods = [
    () => getLocationFromTimezone(),    // ~10ms
    () => getLocationFromLanguage(),    // ~5ms
    () => getLocationFromConnection()   // ~1ms
  ];

  return Promise.race(
    fastMethods.map(async method => {
      try {
        const result = await method();
        if (result && result.coords) return result;
        throw new Error('Method failed');
      } catch (error) {
        throw error;
      }
    })
  );
};

const getAutomaticLocation = async () => {
  // Dual-track approach: Fast result + Accurate result
  const preferences = LocationCache.userPreferences;

  // Start both tracks simultaneously
  const fastTrack = getFastLocation().catch(() => null);
  const accurateTrack = getHybridLocation().catch(() => null);

  // Return fast result first, then upgrade to accurate result
  const fastResult = await fastTrack;
  if (fastResult) {
    // Start accurate track in background for UI upgrade
    accurateTrack.then(accurateResult => {
      if (accurateResult) {
        const shouldUpgrade =
          accurateResult.accuracy < fastResult.accuracy || // Better accuracy
          accurateResult.arabic.locationName !== fastResult.arabic.locationName; // Different location

        if (shouldUpgrade) {
          LocationCache.set(accurateResult);
          locationManager.triggerLocationUpdate(accurateResult);
        }
      }
    });
    return fastResult;
  }

  // If fast track failed, wait for accurate track
  const accurateResult = await accurateTrack;
  if (accurateResult) return accurateResult;

  // Try last successful method
  if (preferences.lastSuccessfulMethod && methodMap[preferences.lastSuccessfulMethod]) {
    try {
      const result = await methodMap[preferences.lastSuccessfulMethod]();
      if (result && result.coords) return result;
    } catch (error) {
      // Continue to other methods
    }
  }

  // Try methods in order of success rate
  const sortedMethods = Object.entries(preferences.methodSuccessRates)
    .sort(([,a], [,b]) => b - a)
    .map(([method]) => method)
    .filter(method => methodMap[method]);

  for (const method of sortedMethods) {
    try {
      const result = await methodMap[method]();
      if (result && result.coords) return result;
    } catch (error) {
      // Continue to next method
    }
  }

  // Fallback to all methods in order of expected accuracy
  const fallbackMethods = [
    () => getLocationFromIP(),        // Most accurate
    () => getLocationFromTimezone(),  // Good accuracy
    () => getLocationFromLanguage(),  // Moderate accuracy
    () => getLocationFromConnection() // Basic accuracy
  ];

  for (const method of fallbackMethods) {
    try {
      const result = await method();
      if (result && result.coords) return result;
    } catch (error) {
      // Continue to next method
    }
  }
  return null;
};

const getLocationFromTimezone = async () => {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const timezoneMap = {
    'Asia/Beirut': { lat: 33.8938, lng: 35.5018, cityAr: 'بيروت', countryAr: 'لبنان', cityEn: 'Beirut', countryEn: 'Lebanon' },
    'Asia/Damascus': { lat: 33.5138, lng: 36.2765, cityAr: 'دمشق', countryAr: 'سوريا', cityEn: 'Damascus', countryEn: 'Syria' },
    'Asia/Amman': { lat: 31.9454, lng: 35.9284, cityAr: 'عمان', countryAr: 'الأردن', cityEn: 'Amman', countryEn: 'Jordan' },
    'Asia/Jerusalem': { lat: 31.7683, lng: 35.2137, cityAr: 'القدس', countryAr: 'فلسطين', cityEn: 'Jerusalem', countryEn: 'Palestine' },
    'Europe/Istanbul': { lat: 41.0082, lng: 28.9784, cityAr: 'إسطنبول', countryAr: 'تركيا', cityEn: 'Istanbul', countryEn: 'Turkey' },
    'Africa/Cairo': { lat: 30.0444, lng: 31.2357, cityAr: 'القاهرة', countryAr: 'مصر', cityEn: 'Cairo', countryEn: 'Egypt' },
    'Asia/Riyadh': { lat: 24.7136, lng: 46.6753, cityAr: 'الرياض', countryAr: 'السعودية', cityEn: 'Riyadh', countryEn: 'Saudi Arabia' },
    'Asia/Dubai': { lat: 25.2048, lng: 55.2708, cityAr: 'دبي', countryAr: 'الإمارات', cityEn: 'Dubai', countryEn: 'UAE' },
    'Asia/Kuwait': { lat: 29.3759, lng: 47.9774, cityAr: 'الكويت', countryAr: 'الكويت', cityEn: 'Kuwait City', countryEn: 'Kuwait' },
    'Asia/Doha': { lat: 25.2854, lng: 51.5310, cityAr: 'الدوحة', countryAr: 'قطر', cityEn: 'Doha', countryEn: 'Qatar' },
    'Asia/Muscat': { lat: 23.5859, lng: 58.4059, cityAr: 'مسقط', countryAr: 'عمان', cityEn: 'Muscat', countryEn: 'Oman' },
    'Asia/Bahrain': { lat: 26.0667, lng: 50.5577, cityAr: 'المنامة', countryAr: 'البحرين', cityEn: 'Manama', countryEn: 'Bahrain' }
  };

  const location = timezoneMap[timezone];
  if (location) {
    return {
      coords: { lat: location.lat, lng: location.lng, accuracy: 25000 },
      arabic: { locationName: `${location.cityAr}، ${location.countryAr}` },
      english: { city: location.cityEn, country: location.countryEn },
      method: 'timezone',
      accuracy: 25000
    };
  }
  return null;
};

const translateToArabic = (cityEn, countryEn) => {
  const cityTranslations = {
    'Beirut': 'بيروت', 'Damascus': 'دمشق', 'Aleppo': 'حلب', 'Amman': 'عمان', 'Jerusalem': 'القدس',
    'Cairo': 'القاهرة', 'Alexandria': 'الإسكندرية', 'Riyadh': 'الرياض', 'Jeddah': 'جدة', 'Mecca': 'مكة',
    'Medina': 'المدينة', 'Dubai': 'دبي', 'Abu Dhabi': 'أبو ظبي', 'Doha': 'الدوحة', 'Kuwait City': 'الكويت',
    'Manama': 'المنامة', 'Muscat': 'مسقط', 'Istanbul': 'إسطنبول', 'Ankara': 'أنقرة', 'Sidon': 'صيدا',
    'Tripoli': 'طرابلس', 'Tyre': 'صور', 'Baalbek': 'بعلبك', 'Zahle': 'زحلة', 'Jounieh': 'جونيه'
  };

  const countryTranslations = {
    'Lebanon': 'لبنان', 'Syria': 'سوريا', 'Jordan': 'الأردن', 'Palestine': 'فلسطين', 'Egypt': 'مصر',
    'Saudi Arabia': 'السعودية', 'UAE': 'الإمارات', 'Qatar': 'قطر', 'Kuwait': 'الكويت', 'Bahrain': 'البحرين',
    'Oman': 'عمان', 'Turkey': 'تركيا', 'Iraq': 'العراق', 'Iran': 'إيران', 'Morocco': 'المغرب',
    'Tunisia': 'تونس', 'Algeria': 'الجزائر', 'Libya': 'ليبيا', 'Sudan': 'السودان'
  };

  return {
    cityAr: cityTranslations[cityEn] || cityEn || 'مدينة',
    countryAr: countryTranslations[countryEn] || countryEn || 'دولة'
  };
};

const getLocationFromIP = async () => {
  const ipServices = [
    {
      url: 'https://ipapi.co/json/',
      timeout: 1500,
      parser: (data) => ({
        lat: data.latitude,
        lng: data.longitude,
        cityEn: data.city,
        countryEn: data.country_name,
        accuracy: data.accuracy || 5000
      })
    },
    {
      url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',
      timeout: 1500,
      parser: (data) => ({
        lat: parseFloat(data.latitude),
        lng: parseFloat(data.longitude),
        cityEn: data.city,
        countryEn: data.country_name,
        accuracy: 8000
      })
    },
    {
      url: 'https://ipinfo.io/json',
      timeout: 1200,
      parser: (data) => {
        const [lat, lng] = (data.loc || '').split(',').map(parseFloat);
        return {
          lat, lng,
          cityEn: data.city,
          countryEn: data.country,
          accuracy: 10000
        };
      }
    }
  ];

  for (const service of ipServices) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), service.timeout);

      const response = await fetch(service.url, {
        signal: controller.signal,
        headers: { 'Accept': 'application/json' }
      });

      clearTimeout(timeoutId);

      if (!response.ok) continue;

      const data = await response.json();
      const parsed = service.parser(data);

      if (parsed.lat && parsed.lng && !isNaN(parsed.lat) && !isNaN(parsed.lng)) {
        const { cityAr, countryAr } = translateToArabic(parsed.cityEn, parsed.countryEn);

        return {
          coords: { lat: parsed.lat, lng: parsed.lng, accuracy: parsed.accuracy },
          arabic: { locationName: `${cityAr}، ${countryAr}` },
          english: { city: parsed.cityEn || 'City', country: parsed.countryEn || 'Country' },
          method: 'ip',
          accuracy: parsed.accuracy
        };
      }
    } catch (error) {
      // Continue to next service
      continue;
    }
  }
  return null;
};

const getLocationFromLanguage = async () => {
  const language = navigator.language || navigator.languages?.[0];
  const languageMap = {
    'ar': { lat: 33.8938, lng: 35.5018, cityAr: 'بيروت', countryAr: 'لبنان', cityEn: 'Beirut', countryEn: 'Lebanon' },
    'ar-LB': { lat: 33.8938, lng: 35.5018, cityAr: 'بيروت', countryAr: 'لبنان', cityEn: 'Beirut', countryEn: 'Lebanon' },
    'ar-SY': { lat: 33.5138, lng: 36.2765, cityAr: 'دمشق', countryAr: 'سوريا', cityEn: 'Damascus', countryEn: 'Syria' },
    'ar-JO': { lat: 31.9454, lng: 35.9284, cityAr: 'عمان', countryAr: 'الأردن', cityEn: 'Amman', countryEn: 'Jordan' },
    'ar-PS': { lat: 31.7683, lng: 35.2137, cityAr: 'القدس', countryAr: 'فلسطين', cityEn: 'Jerusalem', countryEn: 'Palestine' },
    'ar-EG': { lat: 30.0444, lng: 31.2357, cityAr: 'القاهرة', countryAr: 'مصر', cityEn: 'Cairo', countryEn: 'Egypt' },
    'ar-SA': { lat: 24.7136, lng: 46.6753, cityAr: 'الرياض', countryAr: 'السعودية', cityEn: 'Riyadh', countryEn: 'Saudi Arabia' },
    'ar-AE': { lat: 25.2048, lng: 55.2708, cityAr: 'دبي', countryAr: 'الإمارات', cityEn: 'Dubai', countryEn: 'UAE' },
    'ar-KW': { lat: 29.3759, lng: 47.9774, cityAr: 'الكويت', countryAr: 'الكويت', cityEn: 'Kuwait City', countryEn: 'Kuwait' },
    'ar-QA': { lat: 25.2854, lng: 51.5310, cityAr: 'الدوحة', countryAr: 'قطر', cityEn: 'Doha', countryEn: 'Qatar' },
    'ar-OM': { lat: 23.5859, lng: 58.4059, cityAr: 'مسقط', countryAr: 'عمان', cityEn: 'Muscat', countryEn: 'Oman' },
    'ar-BH': { lat: 26.0667, lng: 50.5577, cityAr: 'المنامة', countryAr: 'البحرين', cityEn: 'Manama', countryEn: 'Bahrain' },
    'ar-IQ': { lat: 33.3152, lng: 44.3661, cityAr: 'بغداد', countryAr: 'العراق', cityEn: 'Baghdad', countryEn: 'Iraq' },
    'tr': { lat: 41.0082, lng: 28.9784, cityAr: 'إسطنبول', countryAr: 'تركيا', cityEn: 'Istanbul', countryEn: 'Turkey' },
    'tr-TR': { lat: 39.9334, lng: 32.8597, cityAr: 'أنقرة', countryAr: 'تركيا', cityEn: 'Ankara', countryEn: 'Turkey' }
  };

  const location = languageMap[language] || languageMap[language?.split('-')[0]];
  if (location) {
    return {
      coords: { lat: location.lat, lng: location.lng, accuracy: 75000 },
      arabic: { locationName: `${location.cityAr}، ${location.countryAr}` },
      english: { city: location.cityEn, country: location.countryEn },
      method: 'language',
      accuracy: 75000
    };
  }
  return null;
};

const getLocationFromConnection = async () => {
  const connection = navigator.connection;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Use connection speed and timezone to infer urban vs rural location
  if (connection?.effectiveType === '4g' && connection?.downlink > 15) {
    // Very high-speed connection suggests major urban center
    const urbanCenters = {
      'Asia/Beirut': { lat: 33.8938, lng: 35.5018, cityAr: 'بيروت', countryAr: 'لبنان' },
      'Asia/Damascus': { lat: 33.5138, lng: 36.2765, cityAr: 'دمشق', countryAr: 'سوريا' },
      'Asia/Amman': { lat: 31.9454, lng: 35.9284, cityAr: 'عمان', countryAr: 'الأردن' },
      'Africa/Cairo': { lat: 30.0444, lng: 31.2357, cityAr: 'القاهرة', countryAr: 'مصر' },
      'Asia/Riyadh': { lat: 24.7136, lng: 46.6753, cityAr: 'الرياض', countryAr: 'السعودية' },
      'Asia/Dubai': { lat: 25.2048, lng: 55.2708, cityAr: 'دبي', countryAr: 'الإمارات' }
    };

    const center = urbanCenters[timezone];
    if (center) {
      return {
        coords: { lat: center.lat, lng: center.lng, accuracy: 30000 },
        arabic: { locationName: `${center.cityAr}، ${center.countryAr}` },
        english: { city: center.cityAr, country: center.countryAr },
        method: 'connection',
        accuracy: 30000
      };
    }
  } else if (connection?.effectiveType === '3g' || connection?.effectiveType === '4g') {
    // Moderate connection suggests secondary city
    return {
      coords: { lat: 33.5563, lng: 35.3781, accuracy: 40000 },
      arabic: { locationName: 'صيدا، لبنان' },
      english: { city: 'Sidon', country: 'Lebanon' },
      method: 'connection',
      accuracy: 40000
    };
  }
  return null;
};

const getHybridLocation = async () => {
  // Progressive accuracy: Start with fast methods, add slower ones
  const fastMethods = [
    getLocationFromTimezone(),
    getLocationFromLanguage()
  ];

  const slowMethods = [
    getLocationFromIP()
  ];

  // Get fast results first
  const fastResults = await Promise.allSettled(fastMethods);
  const fastData = fastResults
    .filter(result => result.status === 'fulfilled' && result.value)
    .map(result => result.value)
    .filter(location => location.coords && location.coords.lat && location.coords.lng);

  // Start slow methods in parallel
  const slowPromise = Promise.allSettled(slowMethods);

  // If we have fast results, return immediately and upgrade later
  if (fastData.length > 0) {
    const fastResult = calculateWeightedLocation(fastData);

    // Upgrade with slow results in background
    slowPromise.then(slowResults => {
      const slowData = slowResults
        .filter(result => result.status === 'fulfilled' && result.value)
        .map(result => result.value)
        .filter(location => location.coords && location.coords.lat && location.coords.lng);

      if (slowData.length > 0) {
        const combinedData = [...fastData, ...slowData];
        const upgradedResult = calculateWeightedLocation(combinedData);

        // Check if upgrade is significantly better (more than 5km improvement)
        const improvementThreshold = 5000; // 5km
        const improvement = fastResult.accuracy - upgradedResult.accuracy;

        if (improvement > improvementThreshold) {
          LocationCache.set(upgradedResult);
          locationManager.triggerLocationUpdate(upgradedResult);
        }
      }
    });

    return fastResult;
  }

  // If no fast results, wait for slow methods
  const slowResults = await slowPromise;
  const slowData = slowResults
    .filter(result => result.status === 'fulfilled' && result.value)
    .map(result => result.value)
    .filter(location => location.coords && location.coords.lat && location.coords.lng);

  return slowData.length > 0 ? calculateWeightedLocation(slowData) : null;
};

const calculateWeightedLocation = (results) => {
  if (results.length === 0) return null;

  // Weight results by accuracy (lower accuracy number = higher weight)
  const weightedResults = results.map(result => ({
    ...result,
    weight: 1 / (result.accuracy || 50000)
  }));

  // Calculate weighted average coordinates
  const totalWeight = weightedResults.reduce((sum, result) => sum + result.weight, 0);
  const avgLat = weightedResults.reduce((sum, result) => sum + (result.coords.lat * result.weight), 0) / totalWeight;
  const avgLng = weightedResults.reduce((sum, result) => sum + (result.coords.lng * result.weight), 0) / totalWeight;

  // Use the most accurate result's location names
  const mostAccurate = results.reduce((best, current) =>
    (current.accuracy || 50000) < (best.accuracy || 50000) ? current : best
  );

  return {
    coords: { lat: avgLat, lng: avgLng, accuracy: Math.min(...results.map(r => r.accuracy || 50000)) },
    arabic: mostAccurate.arabic,
    english: mostAccurate.english,
    method: 'hybrid',
    accuracy: Math.min(...results.map(r => r.accuracy || 50000))
  };
};

const attemptGeolocation = async () => {
  const attempts = { highAccuracy: 0, lowAccuracy: 0 };

  try {
    attempts.highAccuracy++;
    return await getPosition({
      enableHighAccuracy: true,
      maximumAge: 30000,
      timeout: 1000  // Reduced from 2000ms
    }, 1200);  // Reduced from 2500ms
  } catch (error) {
    try {
      attempts.lowAccuracy++;
      return await getPosition({
        enableHighAccuracy: false,
        maximumAge: 60000,
        timeout: 800   // Reduced from 1500ms
      }, 1000);  // Reduced from 2000ms
    } catch (fallbackError) {
      throw new GeolocationError('All location attempts failed', 'ALL_ATTEMPTS_FAILED', attempts);
    }
  }
};

const validateAndNormalizeCoords = position => {
  if (!position?.coords) throw new Error('Invalid position: missing coordinates');

  const { latitude, longitude, accuracy, altitude, heading, speed } = position.coords;
  
  if (typeof latitude !== 'number' || typeof longitude !== 'number' ||
      !Number.isFinite(latitude) || !Number.isFinite(longitude)) {
    throw new Error('Invalid coordinates: not finite numbers');
  }

  if (Math.abs(latitude) > 90 || Math.abs(longitude) > 180) {
    throw new Error('Invalid coordinates: out of bounds');
  }

  // More sophisticated null island detection
  if (latitude === 0 && longitude === 0) {
    throw new Error('Invalid coordinates: null island location');
  }

  // Detect other suspicious coordinates patterns
  if (Math.abs(latitude) < 0.0001 && Math.abs(longitude) < 0.0001) {
    throw new Error('Invalid coordinates: suspiciously close to origin');
  }

  // Check for obviously incorrect coordinates (e.g., in ocean when expecting land)
  const normalizedCoords = {
    lat: +latitude.toFixed(6),
    lng: +longitude.toFixed(6),
    accuracy: accuracy > 0 ? accuracy : null
  };

  const stableCoords = LocationCache.getStableCoords(normalizedCoords);

  return { 
    ...stableCoords,
    altitude: altitude || null,
    heading: heading >= 0 ? heading : null,
    speed: speed >= 0 ? speed : null,
    timestamp: position.timestamp || Date.now()
  };
};

const fetchWithTimeout = async (url, timeout = 4000) => {
  const controller = new AbortController();
  let timeoutId = null;
  
  try {
    timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, { 
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'Accept-Language': 'ar,en;q=0.8',
        'User-Agent': 'Mozilla/5.0 (compatible; LocationService/1.0)'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format');
    }
    
    return data;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timeout');
    }
    throw error;
  } finally {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
  }
};

const getLocalAreaName = (coords) => {
  const areas = [
    { name: 'صيدا', bounds: { n: 33.590, s: 33.530, e: 35.410, w: 35.350 } }
  ];

  const { lat, lng } = coords;
  for (const area of areas) {
    const { bounds } = area;
    if (lat >= bounds.s && lat <= bounds.n && lng >= bounds.w && lng <= bounds.e) {
      return area.name;
    }
  }
  return null;
};

const reverseGeocode = async coords => {
  const cacheKey = `${coords.lat.toFixed(4)}_${coords.lng.toFixed(4)}`;
  const cached = ReverseGeocodeCache.get(cacheKey);
  if (cached) return cached;

  // Higher precision based on accuracy
  const zoom = coords.accuracy > 500 ? 12 : coords.accuracy > 100 ? 14 : coords.accuracy > 50 ? 16 : 18;
  const localArea = getLocalAreaName(coords);

  if (localArea) {
    const result = {
      arabicData: { address: { city: localArea, country: 'لبنان' } },
      englishData: { address: { city: 'Sidon', country: 'Lebanon' } },
      localArea
    };
    ReverseGeocodeCache.set(cacheKey, result);
    return result;
  }
  
  // Multiple geocoding services for better accuracy and Arabic support
  const geocodingServices = [
    {
      name: 'nominatim',
      arabicUrl: `https://nominatim.openstreetmap.org/reverse?lat=${coords.lat}&lon=${coords.lng}&format=json&accept-language=ar&zoom=${zoom}&addressdetails=1&extratags=1`,
      englishUrl: `https://nominatim.openstreetmap.org/reverse?lat=${coords.lat}&lon=${coords.lng}&format=json&accept-language=en&zoom=${zoom}&addressdetails=1&extratags=1`,
      timeout: 1500
    },
    {
      name: 'photon',
      arabicUrl: `https://photon.komoot.io/reverse?lat=${coords.lat}&lon=${coords.lng}&lang=ar&limit=1`,
      englishUrl: `https://photon.komoot.io/reverse?lat=${coords.lat}&lon=${coords.lng}&lang=en&limit=1`,
      timeout: 1200,
      parser: (data) => data.features?.[0]?.properties || {}
    }
  ];

  for (const service of geocodingServices) {
    try {
      const [arabicResult, englishResult] = await Promise.allSettled([
        fetchWithTimeout(service.arabicUrl, service.timeout),
        fetchWithTimeout(service.englishUrl, service.timeout)
      ]);

      let arabicData = arabicResult.status === 'fulfilled' ? (arabicResult.value || {}) : {};
      let englishData = englishResult.status === 'fulfilled' ? (englishResult.value || {}) : {};

      // Apply service-specific parsing
      if (service.parser) {
        arabicData = service.parser(arabicData);
        englishData = service.parser(englishData);
      }

      // Check if we got meaningful data
      if (arabicData.address || englishData.address ||
          arabicData.city || englishData.city ||
          arabicData.name || englishData.name) {

        const result = {
          arabicData: arabicData,
          englishData: englishData,
          localArea,
          service: service.name
        };

        ReverseGeocodeCache.set(cacheKey, result);
        return result;
      }
    } catch (error) {
      // Continue to next service
      continue;
    }
  }

  // Fallback result
  const result = {
    arabicData: {},
    englishData: {},
    localArea
  };
  ReverseGeocodeCache.set(cacheKey, result);
  return result;
};

const buildLocationInfo = (coords, arabicData, englishData, localArea = null) => {
  const addrAr = arabicData.address || {};
  const addrEn = englishData.address || {};

  const getLocationName = () => {
    if (localArea) {
      return 'صيدا، لبنان';
    }

    // Enhanced Arabic location name building
    const arabicParts = [];

    // Try multiple Arabic field combinations
    const arabicArea = addrAr.hamlet || addrAr.quarter || addrAr.neighbourhood ||
                      addrAr.suburb || addrAr.village || addrAr.residential;
    const arabicCity = addrAr.city || addrAr.town || addrAr.municipality ||
                      addrAr.county || addrAr.state_district;
    const arabicState = addrAr.state || addrAr.province || addrAr.region;
    const arabicCountry = addrAr.country;

    // Build location hierarchy
    if (arabicArea && arabicArea !== arabicCity) arabicParts.push(arabicArea);
    if (arabicCity) arabicParts.push(arabicCity);
    else if (arabicState) arabicParts.push(arabicState);
    if (arabicCountry) arabicParts.push(arabicCountry);

    if (arabicParts.length > 0) {
      return arabicParts.join('، ');
    }

    // Fallback to English with Arabic translation
    const fallbackCity = addrEn.city || addrEn.town || addrEn.municipality;
    const fallbackCountry = addrEn.country;

    if (fallbackCity && fallbackCountry) {
      const { cityAr, countryAr } = translateToArabic(fallbackCity, fallbackCountry);
      return `${cityAr}، ${countryAr}`;
    }

    if (fallbackCountry) {
      const { countryAr } = translateToArabic('', fallbackCountry);
      return countryAr;
    }
    
    // Final fallback
    return 'صيدا، لبنان';
  };

  return {
    coords,
    accuracy: coords.accuracy,
    localArea,
    arabic: { 
      locationName: getLocationName(),
      localArea,
      hamlet: addrAr.hamlet,
      quarter: addrAr.quarter,
      neighborhood: addrAr.neighbourhood || addrAr.suburb,
      village: addrAr.village,
      municipality: addrAr.municipality,
      city: addrAr.city || addrAr.town,
      state: addrAr.state || addrAr.province,
      country: addrAr.country
    },
    english: {
      localArea,
      hamlet: addrEn.hamlet,
      quarter: addrEn.quarter,
      neighborhood: addrEn.neighbourhood || addrEn.suburb,
      village: addrEn.village,
      municipality: addrEn.municipality,
      city: addrEn.city || addrEn.town || 'Sidon',
      state: addrEn.state || addrEn.province || addrEn.region,
      country: addrEn.country || 'Lebanon',
      postalCode: addrEn.postcode
    },
    timestamp: new Date().toISOString()
  };
};

const SIDON_COORDS = { lat: 33.5563, lng: 35.3781 };
const locationManager = {
  activePromise: null,
  updateCallbacks: [],

  setActivePromise(promise) {
    this.activePromise = promise;
  },

  getActivePromise() {
    return this.activePromise;
  },

  clearActivePromise() {
    this.activePromise = null;
  },

  onLocationUpdate(callback) {
    this.updateCallbacks.push(callback);
  },

  triggerLocationUpdate(location) {
    this.updateCallbacks.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('Location update callback failed:', error);
      }
    });
  }
};

const getLocationWithFallback = async () => {
  // Prevent duplicate simultaneous requests
  if (locationManager.getActivePromise()) return locationManager.getActivePromise();

  // Handle stuck processing state
  if (LocationCache.isProcessingTooLong()) {
    LocationCache.stopProcessing();
  }

  if (LocationCache.isProcessing) {
    const waitStart = Date.now();
    while (LocationCache.isProcessing && Date.now() - waitStart < 2000) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    if (locationManager.getActivePromise()) return locationManager.getActivePromise();
  }

  LocationCache.startProcessing();

  const promise = (async () => {
    // Step 1: Check cache first (fastest)
    const cachedResult = LocationCache.get();
    if (cachedResult) {
      return cachedResult;
    }

    // Step 2: Try automatic location detection (no permission needed)
    try {
      const automaticLocation = await getAutomaticLocation();
      if (automaticLocation) {
        LocationCache.set(automaticLocation);
        return automaticLocation;
      }
    } catch (error) {
      // Continue to GPS attempt
    }
    try {
      const extendedCache = LocationCache.get(1800000);
      const defaultLocation = {
        coords: { lat: SIDON_COORDS.lat, lng: SIDON_COORDS.lng, accuracy: null },
        accuracy: null,
        localArea: 'صيدا',
        arabic: { locationName: 'صيدا، لبنان' },
        english: { city: 'Sidon', country: 'Lebanon' },
        timestamp: new Date().toISOString(),
        isDefault: true
      };

      if (!navigator.geolocation) {
        const result = extendedCache || defaultLocation;
        if (!extendedCache) LocationCache.set(result);
        return result;
      }

      // Step 3: Try GPS with fast timeout (only if permission already granted)
      const permissionStatus = await navigator.permissions?.query?.({ name: 'geolocation' }).catch(() => null);
      if (permissionStatus?.state === 'granted') {
        try {
          const position = await attemptGeolocation();
          const coords = validateAndNormalizeCoords(position);
          const { arabicData, englishData, localArea } = await reverseGeocode(coords);
          const locationInfo = buildLocationInfo(coords, arabicData, englishData, localArea);
          LocationCache.set(locationInfo);
          return locationInfo;
        } catch (gpsError) {
          // GPS failed, continue to fallback
        }
      }

      // Step 4: Use extended cache or default location
      const result = extendedCache || defaultLocation;
      if (!extendedCache) LocationCache.set(result);
      return result;

    } catch (error) {
      // Final fallback
      const defaultLocation = {
        coords: { lat: SIDON_COORDS.lat, lng: SIDON_COORDS.lng, accuracy: null },
        accuracy: null,
        localArea: 'صيدا',
        arabic: { locationName: 'صيدا، لبنان' },
        english: { city: 'Sidon', country: 'Lebanon' },
        timestamp: new Date().toISOString(),
        isDefault: true
      };
      LocationCache.set(defaultLocation);
      return defaultLocation;

      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Overall timeout')), 8000)
      );

      try {
        const locationPromise = (async () => {
          const position = await attemptGeolocation();
          const coords = validateAndNormalizeCoords(position);
          const { arabicData, englishData, localArea } = await reverseGeocode(coords);
          return buildLocationInfo(coords, arabicData, englishData, localArea);
        })();

        const locationInfo = await Promise.race([locationPromise, timeoutPromise]);
        LocationCache.set(locationInfo);
        return locationInfo;
      } catch (geolocationError) {
        const result = extendedCache || defaultLocation;
        if (!extendedCache) LocationCache.set(result);
        return result;
      }
    } finally {
      LocationCache.stopProcessing();
      locationManager.clearActivePromise();
    }
  })();

  locationManager.setActivePromise(promise);
  return promise;
};

const fetchPrayerTimesFromAPI = async locationInfo => {
  const today = getBeirutTime();
  const timestamp = Math.floor(today.getTime() / 1000);

  // Use coordinates for more accurate prayer times if available
  let url;
  if (locationInfo.coords && locationInfo.coords.lat && locationInfo.coords.lng) {
    const { lat, lng } = locationInfo.coords;
    url = `https://api.aladhan.com/v1/timings/${timestamp}?latitude=${lat}&longitude=${lng}&method=${CONFIG.prayerMethod}&tune=0,0,0,0,0,0,0,0,0`;
  } else {
    // Fallback to city-based API
    const { city, country } = locationInfo.english;
    url = `https://api.aladhan.com/v1/timingsByCity/${timestamp}?city=${encodeURIComponent(city)}&country=${encodeURIComponent(country)}&method=${CONFIG.prayerMethod}&tune=0,0,0,0,0,0,0,0,0`;
  }

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data || data.code !== 200 || !data.data) {
    throw new Error('Invalid API response');
  }

  const timings = data.data.timings;

  // Store location info used for this prayer times fetch
  const prayerTimesData = {
    Fajr: timings.Fajr,
    Sunrise: timings.Sunrise,
    Dhuhr: timings.Dhuhr,
    Asr: timings.Asr,
    Maghrib: timings.Maghrib,
    Isha: timings.Isha,
    Firstthird: timings.Firstthird,
    Lastthird: timings.Lastthird,
    _locationInfo: {
      coords: locationInfo.coords,
      locationName: locationInfo.arabic.locationName,
      timestamp: Date.now()
    }
  };

  return prayerTimesData;
};

// Enhanced location-aware data manager
const LocationDataManager = {
  currentLocation: null,
  currentPrayerTimes: null,
  currentHijriDate: null,
  lastLocationUpdate: 0,

  // Check if location has changed significantly
  hasLocationChanged(newLocation) {
    if (!this.currentLocation || !newLocation) return true;

    const oldCoords = this.currentLocation.coords;
    const newCoords = newLocation.coords;

    if (!oldCoords || !newCoords) return true;

    // Calculate distance between locations
    const distance = LocationCache.calculateDistance(oldCoords, newCoords);

    // Consider location changed if more than 5km difference
    return distance > 5000;
  },

  // Check if prayer times need refresh based on location
  needsPrayerTimesRefresh(locationInfo) {
    if (!this.currentPrayerTimes || !this.currentPrayerTimes._locationInfo) return true;

    const storedLocation = this.currentPrayerTimes._locationInfo;
    const timeSinceLastUpdate = Date.now() - storedLocation.timestamp;

    // Refresh if more than 1 hour old
    if (timeSinceLastUpdate > 3600000) return true;

    // Refresh if location changed significantly
    if (locationInfo.coords && storedLocation.coords) {
      const distance = LocationCache.calculateDistance(locationInfo.coords, storedLocation.coords);
      return distance > 5000; // 5km threshold
    }

    return false;
  },

  // Update all location-dependent data
  async updateLocationData(forceRefresh = false) {
    const locationInfo = await getLocationWithFallback();
    const locationChanged = this.hasLocationChanged(locationInfo);

    if (locationChanged || forceRefresh) {
      this.currentLocation = locationInfo;
      renderLocationInfo(locationInfo.arabic.locationName);
    }

    // Update prayer times if needed
    if (this.needsPrayerTimesRefresh(locationInfo) || forceRefresh) {
      this.currentPrayerTimes = await fetchPrayerTimesFromAPI(locationInfo);

      if (!validatePrayerTimes(this.currentPrayerTimes)) {
        throw new Error('Invalid prayer times received');
      }
    }

    // Update Hijri date with current location context
    if (locationChanged || forceRefresh || !this.currentHijriDate) {
      try {
        // Use coordinate-based Hijri date if available
        if (locationInfo.coords) {
          this.currentHijriDate = await getHijriDateWithCoords(locationInfo.coords);
        } else {
          this.currentHijriDate = await getHijriDate(getBeirutTime(), this.currentPrayerTimes.Maghrib);
        }
      } catch (error) {
        console.warn('Failed to update Hijri date:', error);
      }
    }

    return {
      location: this.currentLocation,
      prayerTimes: this.currentPrayerTimes,
      hijriDate: this.currentHijriDate
    };
  }
};

const fetchAndDisplayPrayerTimes = async () => {
  const data = await LocationDataManager.updateLocationData();
  return data.prayerTimes;
};

const main = async () => {
  // Register location update callback for progressive enhancement
  locationManager.onLocationUpdate((upgradedLocation) => {
    renderLocationInfo(upgradedLocation.arabic.locationName);
  });

  renderGregorianDate();

  let timings;
  try {
    timings = await fetchAndDisplayPrayerTimes();
    if (!validatePrayerTimes(timings)) return;
  } catch (e) {
    console.error('Failed to fetch prayer times:', e.message);
    return;
  }

  const allPrayers = [
    { key: 'Fajr', icon: 'fa-moon' },
    { key: 'Sunrise', icon: 'fa-sun' },
    { key: 'Dhuhr', icon: 'fa-sun' },
    { key: 'Asr', icon: 'fa-sun' },
    { key: 'Maghrib', icon: 'fa-sunset' },
    { key: 'Isha', icon: 'fa-moon' },
    { key: 'Firstthird', icon: 'fa-star-half-alt' },
    { key: 'Lastthird', icon: 'fa-star-and-crescent' }
  ];

  const prayers = allPrayers.filter(prayer => timings[prayer.key]);
  const timesInMinutes = convertPrayerTimesToMinutes(timings);
  let currentPrayer = null, timeUntil = '';

  const updatePrayerUI = async () => {
    const nowMinutes = getCurrentBeirutMinutes();
    const next = findNextPrayer(nowMinutes, timesInMinutes);
    currentPrayer = next;
    timeUntil = calculateTimeRemaining(nowMinutes, timesInMinutes[next]);

    if (timeUntil === '00:00') {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    await headerStateManager.updateHeaderState(nowMinutes, timings, next, timeUntil);
    renderPrayerTimelineSVG(prayers, timings, next);
  };

  await updatePrayerUI();
  headerStateManager.init();

  setTimeout(() => decorativeBorderManager.init(), 100);

  const indicator = celestialIndicatorManager.getInstance();
  if (indicator) indicator.destroy();
  celestialIndicatorManager.initialize(timings);

  let lastGregorianDate = getBeirutTime().toDateString();
  let lastHijriDay = null;
  let lastMaghribCheck = false;

  const handleDateChange = async (now, currentGregorianDate) => {
    lastGregorianDate = currentGregorianDate;

    // Smooth transition for Gregorian date
    await smoothDateTransition('.date-row:nth-child(2) .date-value', () => {
      renderGregorianDate();
    });

    try {
      const newTimings = await fetchNewPrayerTimes(now);
      Object.assign(timings, newTimings);
      celestialIndicatorManager.getInstance()?.updateTimings(timings);
    } catch (err) {

    }
  };

  const smoothDateTransition = async (selector, updateCallback) => {
    const element = document.querySelector(selector);
    if (!element) return;

    // Add subtle glow effect during transition
    const originalBoxShadow = element.style.boxShadow;

    // Fade out with subtle glow
    element.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out, box-shadow 0.3s ease-out';
    element.style.opacity = '0';
    element.style.transform = 'translateY(-10px)';
    element.style.boxShadow = '0 0 15px rgba(76, 175, 80, 0.3)';

    await new Promise(resolve => setTimeout(resolve, 300));

    // Update content
    await updateCallback();

    // Fade in with gentle animation
    element.style.transform = 'translateY(10px)';
    await new Promise(resolve => setTimeout(resolve, 50));

    element.style.opacity = '1';
    element.style.transform = 'translateY(0)';
    element.style.boxShadow = '0 0 8px rgba(76, 175, 80, 0.2)';

    // Clean up after a brief moment
    setTimeout(() => {
      element.style.transition = '';
      element.style.boxShadow = originalBoxShadow;
    }, 800);
  };



  const updateIslamicCalendarSensitivity = async () => {
    try {
      const now = getBeirutTime();
      const accurateData = await getHijriDate(now);

      hijriDataManager.setData({
        ...accurateData,
        day: accurateData.day,
        month: accurateData.month,
        year: accurateData.year,
        monthLength: accurateData.monthDays || 29
      });

      appState.hijriDate = {
        day: accurateData.day,
        month: accurateData.month,
        year: accurateData.year,
        monthLength: accurateData.monthDays || 29
      };
    } catch (error) {

    }
  };

  const fetchNewPrayerTimes = async now => {
    try {
      // Use the centralized location data manager
      const data = await LocationDataManager.updateLocationData();
      return {
        Fajr: data.prayerTimes.Fajr,
        Sunrise: data.prayerTimes.Sunrise,
        Dhuhr: data.prayerTimes.Dhuhr,
        Asr: data.prayerTimes.Asr,
        Maghrib: data.prayerTimes.Maghrib,
        Isha: data.prayerTimes.Isha,
        Firstthird: data.prayerTimes.Firstthird,
        Lastthird: data.prayerTimes.Lastthird
      };
    } catch (error) {
      throw new Error(`Failed to fetch prayer times: ${error.message}`);
    }
  };

  const buildPrayerTimesUrl = (locationInfo, timestamp) => {
    const baseUrl = `https://api.aladhan.com/v1/timingsByCity/${timestamp}`;
    const params = new URLSearchParams({
      city: locationInfo.english.city,
      country: locationInfo.english.country,
      method: CONFIG.prayerMethod,
      tune: '0,0,0,0,0,0,0,0,0'
    });
    return `${baseUrl}?${params}`;
  };

  const handleMaghribTransition = async (now, currentTime, timings) => {
    const maghribMinutes = convertPrayerTimeToMinutes(timings.Maghrib);
    const isMaghribTransition = currentTime >= maghribMinutes && !lastMaghribCheck;
    
    if (isMaghribTransition) {
      lastMaghribCheck = true;
      lastHijriDay = now.getDate();
      await updateHijriDate();
    } else if (currentTime < maghribMinutes) {
      lastMaghribCheck = false;
    }
  };

  const handleHijriDateUpdate = async (now, currentTime, timings) => {
    const maghribMinutes = convertPrayerTimeToMinutes(timings.Maghrib);

    // Check for Hijri date change around Maghrib time
    if (Math.abs(currentTime - maghribMinutes) <= 2) {
      try {
        const currentHijri = await getHijriDate(now, timings.Maghrib);
        if (lastHijriDay !== null && currentHijri.day !== lastHijriDay) {
          lastHijriDay = currentHijri.day;

          // Smooth transition for Hijri date without reload
          await smoothDateTransition('.date-row:nth-child(1) .date-value', async () => {
            await updateHijriDate();
            await updateIslamicCalendarSensitivity();
          });

          // Refresh prayer times for new Islamic day without reload
          try {
            const newTimings = await fetchNewPrayerTimes(now);
            Object.assign(timings, newTimings);
            celestialIndicatorManager.getInstance()?.updateTimings(timings);
          } catch (err) {

          }
          return;
        }
      } catch (error) {

        return;
      }
    }

    // Initialize Hijri day tracking on first run
    if (lastHijriDay === null) {
      try {
        const initialHijri = await getHijriDate(now, timings.Maghrib);
        lastHijriDay = initialHijri.day;
      } catch (error) {

      }
    }
  };

  const updateHijriDate = async () => {
    try {
      // Use accurate API data if available, otherwise fallback
      const hijriData = hijriDataManager.getData();
      if (hijriData && hijriData.day) {
        const hijriForDisplay = {
          day: hijriData.day,
          month: hijriData.month,
          year: hijriData.year,
          monthLength: hijriData.monthDays || 29
        };
        renderHijriDate(hijriForDisplay);
      } else {
        const hijri = await getHijriDate(getBeirutTime(), timings.Maghrib);
        renderHijriDate(hijri);
      }
    } catch (error) {

    }
  };

  const intervalId = setInterval(async () => {
    const now = getBeirutTime();
    renderCurrentTime();
    
    const currentGregorianDate = now.toDateString();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    if (currentGregorianDate !== lastGregorianDate) {
      await handleDateChange(now, currentGregorianDate);
    }
    
    await handleMaghribTransition(now, currentTime, timings);
    
    if (!headerStateManager.transitionInProgress) {
      await updatePrayerUI();
    }

    await handleHijriDateUpdate(now, currentTime, timings);
  }, 1000);

  window.addEventListener('beforeunload', () => {
    clearInterval(intervalId);
  });

  await contentManager.init();
};

const convertPrayerTimeToMinutes = timeStr => {
  if (!timeStr || typeof timeStr !== 'string') return 0;
  
  const parts = timeStr.split(':');
  if (parts.length !== 2) return 0;
  
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  
  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return 0;
  }
  
  return hours * 60 + minutes;
};

const convertPrayerTimesToMinutes = timings => {
  const result = {};
  for (const [key, value] of Object.entries(timings)) {
    result[key] = convertPrayerTimeToMinutes(value);
  }
  return result;
};

class MoonRenderer {
  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = 36;
    this.canvas.height = 36;
    this.ctx = this.canvas.getContext('2d');
    this.centerX = this.canvas.width / 2;
    this.centerY = this.canvas.height / 2;
    this.moonRadius = 17;
    
    this.lunarFeatures = this.initializeLunarFeatures();
    this.processFeatureCoordinates();
  }

  initializeLunarFeatures() {
    return [
      { lat: -43.31, lon: -11.36, size: 4.2, depth: 0.18, brightness: 1.3, name: 'Tycho', 
        rayLength: 8, rays: 6, centralPeak: true, type: 'complex_crater' },
      { lat: 9.62, lon: -20.08, size: 3.8, depth: 0.14, brightness: 1.15, name: 'Copernicus', 
        rayLength: 5, rays: 4, centralPeak: true, type: 'complex_crater' },
      { lat: 8.10, lon: -38.02, size: 2.8, depth: 0.09, brightness: 1.08, name: 'Kepler', 
        rayLength: 3, rays: 2, centralPeak: false, type: 'simple_crater' },
      { lat: 23.73, lon: -47.36, size: 2.9, depth: 0.11, brightness: 1.25, name: 'Aristarchus', 
        rayLength: 4, rays: 3, centralPeak: true, type: 'complex_crater' },
      { lat: 51.62, lon: -9.41, size: 3.5, depth: 0.06, brightness: 0.45, name: 'Plato', 
        type: 'dark_crater', centralPeak: false },
      { lat: -9.26, lon: 1.86, size: 3.2, depth: 0.08, brightness: 0.68, name: 'Ptolemaeus', 
        centralPeak: false, type: 'simple_crater' },
      { lat: -5.48, lon: -68.64, size: 3.1, depth: 0.07, brightness: 0.35, name: 'Grimaldi', 
        type: 'dark_crater', centralPeak: false },
      
      { lat: 0.68, lon: 23.43, size: 9, brightness: 0.07, name: 'Mare Tranquillitatis', 
        type: 'mare', shape: 'irregular', smoothness: 0.85, albedo: 0.067 },
      { lat: 32.8, lon: -15.6, size: 12, brightness: 0.08, name: 'Mare Imbrium', 
        type: 'mare', shape: 'circular', smoothness: 0.92, albedo: 0.082 },
      { lat: 23.0, lon: 17.5, size: 8, brightness: 0.09, name: 'Mare Serenitatis', 
        type: 'mare', shape: 'oval', smoothness: 0.88, albedo: 0.074 },
      { lat: 15.03, lon: 59.1, size: 6.5, brightness: 0.08, name: 'Mare Crisium', 
        type: 'mare', shape: 'oval', smoothness: 0.82, albedo: 0.078 },
      { lat: -7.8, lon: 51.3, size: 7.5, brightness: 0.07, name: 'Mare Fecunditatis', 
        type: 'mare', shape: 'oval', smoothness: 0.81, albedo: 0.071 },
      { lat: 18.4, lon: -43.3, size: 15, brightness: 0.06, name: 'Oceanus Procellarum', 
        type: 'mare', shape: 'irregular', smoothness: 0.75, albedo: 0.065 },
      
      { lat: 18.9, lon: -3.7, size: 7, brightness: 0.18, name: 'Montes Apenninus', 
        type: 'mountain_range', elevation: 0.09, albedo: 0.185 },
      { lat: 38.4, lon: 10.0, size: 6, brightness: 0.17, name: 'Montes Caucasus', 
        type: 'mountain_range', elevation: 0.07, albedo: 0.175 }
    ];
  }

  processFeatureCoordinates() {
    this.lunarFeatures.forEach(feature => {
      if (feature.lat !== undefined && feature.lon !== undefined) {
        const coords = this.latLonToNormalized(feature.lat, feature.lon);
        feature.x = coords.x;
        feature.y = coords.y;
        feature.visible = coords.visible;
      }
    });
  }

  latLonToNormalized(lat, lon) {
    const latRad = (lat * Math.PI) / 180;
    const lonRad = (lon * Math.PI) / 180;
    
    const cosLat = Math.cos(latRad);
    const cosLon = Math.cos(lonRad);
    const visible = cosLat * cosLon > 0;
    
    if (!visible) return { x: 0, y: 0, visible: false };
    
    const sinLat = Math.sin(latRad);
    const sinLon = Math.sin(lonRad);
    
    const x = 0.5 + sinLon * cosLat * 0.5;
    const y = 0.5 - sinLat * 0.5;
    
    return { 
      x: Math.max(0, Math.min(1, x)), 
      y: Math.max(0, Math.min(1, y)), 
      visible: true 
    };
  }

  getHeightAt(surfaceX, surfaceY) {
    let height = 0;
    
    for (const feature of this.lunarFeatures) {
      if (!feature.visible) continue;
      
      const dx = surfaceX - feature.x;
      const dy = surfaceY - feature.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const radius = feature.size / 100;
      
      if (distance < radius) {
        height += this.calculateFeatureHeight(feature, distance, radius);
      }
    }
    
    return height;
  }

  calculateFeatureHeight(feature, distance, radius) {
    const normalizedDist = distance / radius;
    
    if (feature.type === 'mare') return this.calculateMareHeight(feature, normalizedDist);
    if (feature.type === 'mountain_range') return this.calculateMountainHeight(feature, normalizedDist);
    if (feature.type?.includes('crater')) return this.calculateCraterHeight(feature, normalizedDist);
    
    return 0;
  }

  calculateMareHeight(feature, normalizedDist) {
    const influence = Math.pow(1 - normalizedDist, 2.2) * feature.smoothness;
    return -influence * 0.025;
  }

  calculateMountainHeight(feature, normalizedDist) {
    const influence = Math.pow(1 - normalizedDist, 1.4);
    return influence * (feature.elevation || 0.05);
  }

  calculateCraterHeight(feature, normalizedDist) {
    const depth = feature.depth || 0.1;
    
    if (normalizedDist < 0.12 && feature.centralPeak) {
      const peakHeight = depth * 0.6;
      const peakInfluence = Math.exp(-Math.pow(normalizedDist / 0.08, 2) * 15);
      return peakInfluence * peakHeight;
    }
    
    if (normalizedDist > 0.78 && normalizedDist < 0.95) {
      const rimHeight = depth * 0.4;
      const rimInfluence = Math.exp(-Math.pow((normalizedDist - 0.865) / 0.085, 2) * 8);
      return rimInfluence * rimHeight;
    }
    
    if (normalizedDist < 0.88) {
      const floorDepth = Math.pow(1 - normalizedDist / 0.88, 1.8) * depth;
      return -floorDepth;
    }
    
    return 0;
  }

  getSurfaceNormal(surfaceX, surfaceY, nx, ny, nz) {
    const epsilon = 0.0006;
    const heightCenter = this.getHeightAt(surfaceX, surfaceY);
    const heightRight = this.getHeightAt(surfaceX + epsilon, surfaceY);
    const heightUp = this.getHeightAt(surfaceX, surfaceY + epsilon);
    
    const dhdx = (heightRight - heightCenter) / epsilon;
    const dhdy = (heightUp - heightCenter) / epsilon;
    
    const perturbStrength = 0.35;
    const newNx = nx - dhdx * perturbStrength;
    const newNy = ny - dhdy * perturbStrength;
    const newNz = nz;
    
    const length = Math.sqrt(newNx * newNx + newNy * newNy + newNz * newNz);
    return {
      x: newNx / length,
      y: newNy / length,
      z: newNz / length
    };
  }

  screenToSurface(screenX, screenY, nx, ny, nz) {
    const theta = Math.atan2(nx, nz);
    const phi = Math.asin(-ny);
    
    const surfaceX = 0.5 + theta / Math.PI;
    const surfaceY = 0.5 + phi / Math.PI;
    
    return { x: surfaceX, y: surfaceY };
  }

  drawMoon(phaseAngle) {
    this.ctx.fillStyle = '#1a1a1a';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    const imageData = this.ctx.createImageData(this.canvas.width, this.canvas.height);
    const data = imageData.data;
    
    const phaseRad = (phaseAngle * Math.PI) / 180;
    const sunX = Math.sin(phaseRad);
    const sunY = 0;
    const sunZ = -Math.cos(phaseRad);
    
    const radiusSquared = this.moonRadius * this.moonRadius;
    const invRadius = 1.0 / this.moonRadius;
    
    for (let y = 0; y < this.canvas.height; y++) {
      for (let x = 0; x < this.canvas.width; x++) {
        const pixelIndex = (y * this.canvas.width + x) * 4;
        const color = this.calculatePixelColor(x, y, sunX, sunY, sunZ, radiusSquared, invRadius, phaseAngle);
        
        data[pixelIndex] = color.r;
        data[pixelIndex + 1] = color.g;
        data[pixelIndex + 2] = color.b;
        data[pixelIndex + 3] = color.a;
      }
    }
    
    this.ctx.putImageData(imageData, 0, 0);
    
    // Subtle drop shadow to separate from background
    this.ctx.save();
    this.ctx.globalCompositeOperation = 'destination-over';
    this.ctx.beginPath();
    this.ctx.arc(this.centerX + 0.5, this.centerY + 0.5, this.moonRadius + 1, 0, 2 * Math.PI);
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.08)';
    this.ctx.fill();
    this.ctx.restore();
  }

  calculatePixelColor(x, y, sunX, sunY, sunZ, radiusSquared, inverseRadius, phaseAngle) {
    if (!this.isPixelInsideMoon(x, y, radiusSquared)) {
      return this.transparentColor();
    }

    const { nx, ny, nz } = this.getNormalizedSurfaceNormal(x, y, inverseRadius);
    if (nz === null) {
      return this.transparentColor();
    }

    const surfaceCoords = this.screenToSurface(x, y, nx, ny, nz);
    const normal = this.getSurfaceNormal(surfaceCoords.x, surfaceCoords.y, nx, ny, nz);

    if (this.isPixelIlluminated(normal, sunX, sunY, sunZ)) {
      return this.calculateIlluminatedColor(
        normal.x * sunX + normal.y * sunY + normal.z * sunZ,
        surfaceCoords,
        nz,
        phaseAngle
      );
    }

    return this.calculateEarthshineColor(phaseAngle);
  }

  isPixelInsideMoon(x, y, radiusSquared) {
    const dx = x - this.centerX;
    const dy = y - this.centerY;
    return (dx * dx + dy * dy) <= radiusSquared;
  }

  transparentColor() {
    return { r: 0, g: 0, b: 0, a: 0 };
  }

  getNormalizedSurfaceNormal(x, y, inverseRadius) {
    const dx = x - this.centerX;
    const dy = y - this.centerY;
    const nx = dx * inverseRadius;
    const ny = dy * inverseRadius;
    const zNormalSquared = 1 - nx * nx - ny * ny;

    if (zNormalSquared < 0) return { nx, ny, nz: null };
    return { nx, ny, nz: Math.sqrt(zNormalSquared) };
  }

  isPixelIlluminated(normal, sunX, sunY, sunZ) {
    const dotProduct = normal.x * sunX + normal.y * sunY + normal.z * sunZ;
    return dotProduct > 0.002;
  }

  calculateIlluminatedColor(dotProduct, surfaceCoords, nz, phaseAngle) {
    let brightness = Math.pow(Math.max(0, dotProduct), 0.65) * 0.95;
    let base = { albedo: 0.125, r: 0.99, g: 0.97, b: 0.94 };

    base = this.applyLunarFeatures(surfaceCoords.x, surfaceCoords.y, base);
    brightness += this.calculateMicroTexture(surfaceCoords.x, surfaceCoords.y);
    
    const terminatorSoftness = this.calculateTerminatorSoftness(dotProduct, phaseAngle);
    brightness = brightness * terminatorSoftness;
    
    brightness = Math.min(Math.max(brightness, 0), 1);

    const finalBrightness = brightness * base.albedo * 22.0;
    const limbDarkening = Math.pow(nz, 0.08);

    return {
      r: this.clampColor(finalBrightness * base.r * 255 * limbDarkening),
      g: this.clampColor(finalBrightness * base.g * 255 * limbDarkening),
      b: this.clampColor(finalBrightness * base.b * 255 * limbDarkening),
      a: 255
    };
  }

  calculateTerminatorSoftness(dotProduct, phaseAngle) {
    // Softer terminator transition for thin crescents where scattering effects are more visible
    const phaseRange = Math.abs(phaseAngle % 360);
    const isThinCrescent = phaseRange > 300 || phaseRange < 60;
    
    if (isThinCrescent && dotProduct < 0.15) {
      const softness = Math.pow(dotProduct / 0.15, 0.3);
      return 0.2 + 0.8 * softness;
    }
    
    return 1.0;
  }

  clampColor(value) {
    return Math.min(255, Math.max(0, Math.round(value)));
  }

  applyLunarFeatures(surfaceX, surfaceY, base) {
    for (const feature of this.lunarFeatures) {
      if (!feature.visible) continue;

      const dx = surfaceX - feature.x;
      const dy = surfaceY - feature.y;
      const dist = Math.sqrt(dx * dx + dy * dy);
      const radius = feature.size / 100;

      if (dist < radius) {
        base = this.applyFeatureByType(feature, dist, radius, dx, dy, base);
      }
    }
    return base;
  }

  applyFeatureByType(feature, dist, radius, dx, dy, base) {
    if (feature.type === 'mare') return this.applyMareInfluence(feature, dist, radius, base);
    if (feature.type === 'mountain_range') return this.applyMountainInfluence(feature, dist, radius, base);
    if (feature.type?.includes('crater')) return this.applyCraterInfluence(feature, dist, radius, dx, dy, base);
    return base;
  }

  applyMareInfluence(feature, dist, radius, base) {
    const influence = Math.pow(1 - dist / radius, feature.smoothness || 0.8);
    const albedoRatio = (feature.albedo || 0.07) / base.albedo;
    return {
      albedo: base.albedo * (1 - influence + influence * albedoRatio),
      r: base.r * (1 - influence * 0.008),
      g: base.g * (1 - influence * 0.015),
      b: base.b * (1 - influence * 0.025)
    };
  }

  applyMountainInfluence(feature, dist, radius, base) {
    const influence = Math.pow(1 - dist / radius, 1.2);
    const albedoRatio = (feature.albedo || 0.18) / base.albedo;
    return { ...base, albedo: base.albedo * (1 - influence + influence * albedoRatio) };
  }

  applyCraterInfluence(feature, dist, radius, dx, dy, base) {
    if (feature.brightness) {
      const influence = Math.pow(1 - dist / radius, 1.4);
      const albedoChange = (feature.brightness - 1) * 0.15;
      base.albedo *= (1 + influence * albedoChange);
    }
    if (feature.rays && feature.rayLength && dist > radius * 0.4) {
      base.albedo = this.applyRaySystem(feature, dx, dy, dist, base.albedo);
    }
    return base;
  }

  applyRaySystem(feature, dx, dy, dist, baseAlbedo) {
    const rayAngle = Math.atan2(dy, dx);
    for (let r = 0; r < feature.rays; r++) {
      const targetAngle = (r * 2 * Math.PI) / feature.rays;
      let angleDiff = Math.abs(rayAngle - targetAngle);
      angleDiff = Math.min(angleDiff, 2 * Math.PI - angleDiff);

      const rayDist = dist * 100;
      const rayWidth = 0.025 + (rayDist / feature.rayLength) * 0.012;
      if (angleDiff < rayWidth && rayDist < feature.rayLength) {
        const rayFalloff = Math.exp(-rayDist / (feature.rayLength * 0.4));
        baseAlbedo *= (1 + rayFalloff * 0.12);
      }
    }
    return baseAlbedo;
  }

  calculateMicroTexture(surfaceX, surfaceY) {
    const texScale1 = 600;
    const texScale2 = 1200;
    return (
      Math.sin(surfaceX * texScale1) * Math.cos(surfaceY * texScale1) * 0.004 +
      Math.sin(surfaceX * texScale2) * Math.cos(surfaceY * texScale2) * 0.002
    );
  }

  calculateEarthshineColor(phaseAngle) {
    // Earthshine is strongest when Earth appears fullest from Moon's perspective
    const phaseRange = phaseAngle % 360;
    let earthshinePhase;
    
    if (phaseRange <= 180) {
      earthshinePhase = Math.max(0, Math.sin(((180 - phaseRange) * Math.PI) / 180));
    } else {
      earthshinePhase = Math.max(0, Math.sin(((phaseRange - 180) * Math.PI) / 180));
    }
    
    // Boost for very thin crescents where earthshine is most observable
    const thinCrescentBoost = phaseRange > 330 || phaseRange < 30 ? 1.6 : 1.0;
    const earthshineStrength = earthshinePhase * 0.28 * thinCrescentBoost;

    if (earthshineStrength <= 0.015) return { r: 0, g: 0, b: 0, a: 255 };

    // Bluish tint reflects Earth's atmospheric scattering
    return {
      r: Math.round(earthshineStrength * 45),
      g: Math.round(earthshineStrength * 50),
      b: Math.round(earthshineStrength * 62),
      a: 255
    };
  }
}

const moonRendererManager = {
  instance: null,
  initializationCounter: 0,

  getInstance() {
    return this.instance;
  },

  setInstance(instance) {
    this.instance = instance;
  },

  incrementCounter() {
    return ++this.initializationCounter;
  },

  getCounter() {
    return this.initializationCounter;
  }
};

const initializeMoonRenderer = () => {
  if (moonRendererManager.getInstance()) return;
  moonRendererManager.setInstance(new MoonRenderer());
  document.querySelectorAll('.moon-phase').forEach(setupMoonContainer);
};

function setupMoonContainer(container, index) {
  container.innerHTML = '';
  container.classList.add('moon-container');

  const canvas = createMoonCanvasElement(moonRendererManager.incrementCounter());
  container.appendChild(canvas);

  const phaseAngle = container.dataset.phase
    ? parseFloat(container.dataset.phase)
    : (index * 90) % 360;

  renderMoonToCanvas(canvas, phaseAngle);
}

const createMoonCanvasElement = idNumber => {
  const canvas = document.createElement('canvas');
  canvas.className = 'moon-canvas';
  canvas.id = `moonCanvas_${idNumber}`;
  canvas.width = 36;
  canvas.height = 36;
  return canvas;
};

const renderMoonToCanvas = (canvas, phaseAngle) => {
  const moonRenderer = moonRendererManager.getInstance();
  if (!moonRenderer) return;

  moonRenderer.drawMoon(phaseAngle);
  const ctx = canvas.getContext('2d');
  ctx.drawImage(moonRenderer.canvas, 0, 0);
};

document.addEventListener('DOMContentLoaded', async () => {
  initializeMoonRenderer();
  await initializeCircadianSystem();
  setTimeout(() => { if (typeof main === 'function') main(); }, 100);
});

// Circadian Color Synchronization System
async function initializeCircadianSystem() {
  initializePurposefulImperfection();
  await initializeIslamicCalendarSensitivity();
  updateCircadianColors();
  // Update every 5 minutes for smooth transitions
  setInterval(updateCircadianColors, 300000);

  // Update breathing animation with optimized frequency
  setInterval(updateBreathingRhythm, 1000);

  // Subtle imperfection variations every 30 seconds
  setInterval(updatePurposefulImperfection, 30000);

  // Update Islamic calendar sensitivity daily
  setInterval(updateIslamicCalendarSensitivity, 86400000); // 24 hours
}

function updateBreathingRhythm() {
  const now = new Date();
  const currentHour = now.getHours() + now.getMinutes() / 60;

  // Subtle breathing scale variation - tied to natural rhythms
  const timeBasedBreathing = Math.sin(Date.now() / 6000) * 0.002; // 6-second cycle
  const circadianBreathing = Math.sin(currentHour * Math.PI / 12) * 0.001; // Daily cycle
  const breathingScale = 1 + timeBasedBreathing + circadianBreathing;

  const root = document.documentElement;
  root.style.setProperty('--breathing-scale', breathingScale.toFixed(4));
}

function updateCircadianColors() {
  const now = new Date();
  const currentHour = now.getHours() + now.getMinutes() / 60;

  // Get approximate sunrise/sunset times (simplified calculation)
  const latitude = 33.5; // Approximate latitude for Sidon, Lebanon
  const dayOfYear = Math.floor((now - new Date(now.getFullYear(), 0, 0)) / 86400000);

  // Simplified sunrise/sunset calculation
  const solarDeclination = 23.45 * Math.sin((360 * (284 + dayOfYear) / 365) * Math.PI / 180);
  const hourAngle = Math.acos(-Math.tan(latitude * Math.PI / 180) * Math.tan(solarDeclination * Math.PI / 180));
  const sunrise = 12 - hourAngle * 12 / Math.PI;
  const sunset = 12 + hourAngle * 12 / Math.PI;

  // Calculate circadian warmth/coolness based on sun position
  let warmth = 0;
  let coolness = 0;

  if (currentHour >= sunrise && currentHour <= sunset) {
    // Daytime - warmer tones
    const dayProgress = (currentHour - sunrise) / (sunset - sunrise);
    if (dayProgress < 0.5) {
      // Morning to noon - increasing warmth
      warmth = Math.sin(dayProgress * Math.PI) * 0.8;
    } else {
      // Noon to evening - decreasing warmth
      warmth = Math.sin((1 - dayProgress) * Math.PI) * 0.6;
    }
  } else {
    // Nighttime - cooler tones
    const nightProgress = currentHour > sunset ?
      (currentHour - sunset) / (24 - sunset + sunrise) :
      (currentHour + 24 - sunset) / (24 - sunset + sunrise);
    coolness = Math.sin(nightProgress * Math.PI) * 0.4;
  }

  // Seasonal adjustment
  const month = now.getMonth();
  let seasonalAdjustment = 0;
  if (month >= 11 || month <= 2) {
    // Winter - slightly warmer tones
    seasonalAdjustment = 0.1;
  } else if (month >= 5 && month <= 8) {
    // Summer - slightly cooler tones
    seasonalAdjustment = -0.1;
  }

  // Natural texture opacity varies subtly with time
  const textureOpacity = 0.015 + Math.sin(currentHour * Math.PI / 12) * 0.01;

  // Apply to CSS variables
  const root = document.documentElement;
  root.style.setProperty('--circadian-warmth', warmth.toFixed(3));
  root.style.setProperty('--circadian-coolness', coolness.toFixed(3));
  root.style.setProperty('--seasonal-adjustment', seasonalAdjustment.toFixed(3));
  root.style.setProperty('--natural-texture-opacity', textureOpacity.toFixed(4));


}

// Enhanced natural texture patterns
function applyNaturalTextures() {
  const textureElements = document.querySelectorAll('.prayer-timeline-section, .hadith-section');

  textureElements.forEach(element => {
    // Add subtle organic texture variations
    const textureVariation = Math.random() * 0.005 + 0.01;
    element.style.setProperty('--local-texture-opacity', textureVariation.toFixed(4));
  });
}

// Initialize texture variations
setTimeout(applyNaturalTextures, 1000);

// Purposeful Imperfection System - Golden Ratio Based
function initializePurposefulImperfection() {
  const goldenRatio = 1.618;
  const phi = 0.618; // Golden ratio conjugate

  // Generate contextually sensitive imperfections
  const baseAsymmetryX = (Math.random() - 0.5) * 2 * phi;
  const baseAsymmetryY = (Math.random() - 0.5) * 2 * goldenRatio;
  const baseRotation = (Math.random() - 0.5) * phi;
  const baseScale = (Math.random() - 0.5) * 0.004;

  const root = document.documentElement;
  root.style.setProperty('--micro-asymmetry-x', `${baseAsymmetryX}px`);
  root.style.setProperty('--micro-asymmetry-y', `${baseAsymmetryY}px`);
  root.style.setProperty('--rotation-imperfection', `${baseRotation}deg`);
  root.style.setProperty('--scale-imperfection', baseScale.toFixed(6));
}

function updatePurposefulImperfection() {
  const goldenRatio = 1.618;
  const phi = 0.618;

  // Subtle variations that feel natural, not algorithmic
  const timeVariation = Math.sin(Date.now() / 120000) * 0.1; // 2-minute cycle
  const naturalVariation = (Math.random() - 0.5) * 0.05;

  const currentAsymmetryX = parseFloat(getComputedStyle(document.documentElement)
    .getPropertyValue('--micro-asymmetry-x').replace('px', ''));
  const currentAsymmetryY = parseFloat(getComputedStyle(document.documentElement)
    .getPropertyValue('--micro-asymmetry-y').replace('px', ''));

  // Gentle drift following golden ratio principles
  const newAsymmetryX = currentAsymmetryX + (timeVariation + naturalVariation) * phi;
  const newAsymmetryY = currentAsymmetryY + (timeVariation - naturalVariation) * goldenRatio * 0.5;

  // Keep within natural bounds
  const boundedX = Math.max(-2, Math.min(2, newAsymmetryX));
  const boundedY = Math.max(-3, Math.min(3, newAsymmetryY));

  const root = document.documentElement;
  root.style.setProperty('--micro-asymmetry-x', `${boundedX}px`);
  root.style.setProperty('--micro-asymmetry-y', `${boundedY}px`);
}

// Enhanced natural texture patterns with contextual sensitivity
function applyContextualTextures() {
  const now = new Date();
  const hour = now.getHours();

  // Texture intensity varies with time of day
  let textureIntensity = 1;
  if (hour >= 6 && hour <= 18) {
    // Daytime - more subtle textures
    textureIntensity = 0.7 + Math.sin((hour - 6) * Math.PI / 12) * 0.3;
  } else {
    // Nighttime - slightly more pronounced textures for warmth
    textureIntensity = 0.9 + Math.sin((hour + 6) * Math.PI / 12) * 0.2;
  }

  const textureElements = document.querySelectorAll('.prayer-timeline-section, .hadith-section, .current-prayer-status');

  textureElements.forEach((element, index) => {
    // Each element gets slightly different texture characteristics
    const goldenVariation = (index * 0.618) % 1;
    const textureVariation = (Math.random() * 0.008 + 0.012) * textureIntensity * (1 + goldenVariation * 0.2);
    element.style.setProperty('--local-texture-opacity', textureVariation.toFixed(6));
  });
}

// Replace the original texture function
setTimeout(applyContextualTextures, 1000);
setInterval(applyContextualTextures, 300000); // Update every 5 minutes

// Prayer Point Position Verification Function
window.verifyPrayerPositions = function() {
  if (!appState.prayerTimes) {
    console.log('❌ No prayer times available for verification');
    return;
  }

  console.log('🕌 Prayer Point Position Verification');
  console.log('=====================================');

  const fajrMinutes = timeToMinutes(appState.prayerTimes.Fajr);
  const maghribMinutes = timeToMinutes(appState.prayerTimes.Maghrib);
  const { nightDuration, dayDuration } = calculateDayNightDurations(fajrMinutes, maghribMinutes);

  console.log(`📊 Duration Analysis:`);
  console.log(`   Night Duration: ${(nightDuration/60).toFixed(1)} hours (${nightDuration} minutes)`);
  console.log(`   Day Duration: ${(dayDuration/60).toFixed(1)} hours (${dayDuration} minutes)`);
  console.log(`   Fajr: ${appState.prayerTimes.Fajr} (${fajrMinutes} minutes)`);
  console.log(`   Maghrib: ${appState.prayerTimes.Maghrib} (${maghribMinutes} minutes)`);

  const prayerKeys = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  if (appState.prayerTimes.Firstthird) prayerKeys.push('Firstthird');
  if (appState.prayerTimes.Lastthird) prayerKeys.push('Lastthird');

  console.log(`\n🎯 Prayer Point Angles (12-hour day/night model):`);

  prayerKeys.forEach(prayer => {
    if (!appState.prayerTimes[prayer]) return;

    const timeMinutes = timeToMinutes(appState.prayerTimes[prayer]);
    const isNight = isNightTime(timeMinutes, maghribMinutes, fajrMinutes);

    let angle;
    if (isNight) {
      angle = calculateNightAngle(timeMinutes, maghribMinutes, nightDuration);
    } else {
      angle = calculateDayAngle(timeMinutes, fajrMinutes, dayDuration);
    }

    const period = isNight ? 'Night' : 'Day';
    const arcPosition = isNight ?
      `${((timeMinutes - maghribMinutes + 1440) % 1440 / nightDuration * 100).toFixed(1)}% through night` :
      `${((timeMinutes - fajrMinutes + 1440) % 1440 / dayDuration * 100).toFixed(1)}% through day`;

    console.log(`   ${prayer}: ${appState.prayerTimes[prayer]} → ${angle.toFixed(1)}° (${period} - ${arcPosition})`);
  });

  console.log(`\n✅ Verification complete. Night prayers should be 0°-180°, day prayers 180°-360°`);
};

// Islamic Calendar Sensitivity System
async function initializeIslamicCalendarSensitivity() {
  await updateIslamicCalendarSensitivity();

  // Ensure the UI is updated with accurate Hijri date
  const hijriData = hijriDataManager.getData();
  if (hijriData) {
    const hijriForDisplay = {
      day: hijriData.day,
      month: hijriData.month,
      year: hijriData.year,
      monthLength: hijriData.monthDays || 29
    };
    renderHijriDate(hijriForDisplay);
  }
}

// Store the current Hijri data globally for access
const hijriDataManager = {
  currentData: null,

  setData(data) {
    this.currentData = data;
  },

  getData() {
    return this.currentData;
  }
};

// Synchronize Hijri data between different systems
function synchronizeHijriData(accurateData) {
  if (!accurateData) return;

  hijriDataManager.setData({
    ...accurateData,
    day: accurateData.day,
    month: accurateData.month,
    year: accurateData.year,
    monthLength: accurateData.monthDays || 29
  });

  // Update the state management system
  appState.hijriDate = {
    day: accurateData.day,
    month: accurateData.month,
    year: accurateData.year,
    monthLength: accurateData.monthDays || 29
  };
}

async function updateIslamicCalendarSensitivity() {
  const gregorianDate = new Date();

  // Use shared hijri data if available, otherwise fetch new data
  let hijriDate;
  const hijriData = hijriDataManager.getData();
  if (hijriData && hijriData.monthName) {
    hijriDate = hijriData;
  } else {
    hijriDate = await getAccurateHijriDate();
    // Synchronize data across all systems
    synchronizeHijriData(hijriDate);
  }

  // Reset all spiritual variables
  let spiritualWarmth = 0;
  let sacredReverence = 0;
  let celebrationJoy = 0;
  let fastingSerenity = 0;
  let pilgrimageDevotionValue = 0;

  // Check for Islamic calendar events using accurate data
  const islamicEvents = detectIslamicEvents(hijriDate, gregorianDate);

  // Apply contextual sensitivity based on events
  islamicEvents.forEach(event => {
    switch(event.type) {
      case 'ramadan':
        fastingSerenity = 0.8;
        spiritualWarmth = 0.6;
        sacredReverence = 0.7;
        break;
      case 'eid-fitr':
        celebrationJoy = 1.0;
        spiritualWarmth = 0.9;
        break;
      case 'eid-adha':
        celebrationJoy = 0.9;
        pilgrimageDevotionValue = 0.8;
        spiritualWarmth = 0.8;
        break;
      case 'hajj-period':
        pilgrimageDevotionValue = 1.0;
        sacredReverence = 0.9;
        spiritualWarmth = 0.7;
        break;
      case 'day-of-arafah':
        pilgrimageDevotionValue = 1.0;
        sacredReverence = 1.0;
        spiritualWarmth = 0.8;
        break;
      case 'laylat-al-qadr':
        sacredReverence = 1.0;
        spiritualWarmth = 0.9;
        fastingSerenity = 0.9;
        break;
      case 'ashura':
        sacredReverence = 0.8;
        spiritualWarmth = 0.6;
        break;
      case 'sacred-month':
        sacredReverence = 0.5;
        spiritualWarmth = 0.4;
        break;
      case 'jummah':
        spiritualWarmth = 0.3;
        sacredReverence = 0.2;
        break;
      case 'white-days':
        fastingSerenity = 0.4;
        spiritualWarmth = 0.3;
        break;
    }
  });

  // Apply to CSS variables
  const root = document.documentElement;
  root.style.setProperty('--spiritual-warmth', spiritualWarmth.toFixed(3));
  root.style.setProperty('--sacred-reverence', sacredReverence.toFixed(3));
  root.style.setProperty('--celebration-joy', celebrationJoy.toFixed(3));
  root.style.setProperty('--fasting-serenity', fastingSerenity.toFixed(3));
  root.style.setProperty('--pilgrimage-devotion', pilgrimageDevotionValue.toFixed(3));


}

function detectIslamicEvents(hijriDate, gregorianDate) {
  const events = [];
  const { year, month, day, monthDays, holidays } = hijriDate;
  const dayOfWeek = gregorianDate.getDay();

  // Add API-provided holidays
  if (holidays && holidays.length > 0) {
    holidays.forEach(holiday => {
      events.push({ type: 'api-holiday', name: holiday });
    });
  }

  // Sacred Months
  if ([1, 7, 11, 12].includes(month)) {
    events.push({ type: 'sacred-month', name: getSacredMonthName(month) });
  }

  // Ramadan (9th month) - using accurate month length
  if (month === 9) {
    events.push({ type: 'ramadan', name: 'شهر رمضان المبارك' });

    // Laylat al-Qadr (odd nights of last 10 days) - using accurate month length
    const lastTenDays = monthDays - 9;
    if (day >= lastTenDays && day % 2 === 1) {
      events.push({ type: 'laylat-al-qadr', name: 'ليلة القدر' });
    }
  }

  // Eid al-Fitr (1st of Shawwal)
  if (month === 10 && day === 1) {
    events.push({ type: 'eid-fitr', name: 'عيد الفطر المبارك' });
  }

  // Dhul Hijjah events
  if (month === 12) {
    if (day >= 8 && day <= 13) {
      events.push({ type: 'hajj-period', name: 'موسم الحج' });
    }
    if (day === 9) {
      events.push({ type: 'day-of-arafah', name: 'يوم عرفة' });
    }
    if (day >= 10 && day <= 13) {
      events.push({ type: 'eid-adha', name: 'عيد الأضحى المبارك' });
    }
  }

  // Ashura (10th of Muharram)
  if (month === 1 && day === 10) {
    events.push({ type: 'ashura', name: 'يوم عاشوراء' });
  }

  // Jumu'ah (Friday)
  if (dayOfWeek === 5) {
    events.push({ type: 'jummah', name: 'يوم الجمعة' });
  }

  // White Days (13th, 14th, 15th of each month)
  if ([13, 14, 15].includes(day)) {
    events.push({ type: 'white-days', name: 'الأيام البيض' });
  }

  return events;
}

function getSacredMonthName(month) {
  const names = {
    1: 'شهر محرم الحرام',
    7: 'شهر رجب الحرام',
    11: 'شهر ذو القعدة الحرام',
    12: 'شهر ذو الحجة الحرام'
  };
  return names[month] || '';
}

// Enhanced Hijri date using Al Adhan API with location awareness
async function getHijriDateWithCoords(coords) {
  try {
    const { lat, lng } = coords;
    const response = await fetch(`https://api.aladhan.com/v1/timings?latitude=${lat}&longitude=${lng}&method=5`);
    const data = await response.json();

    if (data.code === 200) {
      const hijriData = data.data.date.hijri;
      return {
        year: parseInt(hijriData.year),
        month: hijriData.month.number,
        day: parseInt(hijriData.day),
        monthName: hijriData.month.ar,
        monthDays: hijriData.month.days,
        weekday: hijriData.weekday.ar,
        holidays: hijriData.holidays || [],
        timings: data.data.timings,
        _coords: coords
      };
    }
  } catch (error) {
    console.warn('Failed to fetch Hijri date with coordinates:', error);
  }

  // Fallback to approximation if API fails
  return gregorianToHijriFallback(new Date());
}

// Enhanced Hijri date using Al Adhan API
async function getAccurateHijriDate() {
  try {
    // Try to use current location if available
    const currentLocation = LocationDataManager.currentLocation;
    if (currentLocation && currentLocation.coords) {
      return await getHijriDateWithCoords(currentLocation.coords);
    }

    // Fallback to Sidon, Lebanon coordinates (33.5531, 35.3781)
    const response = await fetch('https://api.aladhan.com/v1/timings?latitude=33.5531&longitude=35.3781&method=5');
    const data = await response.json();

    if (data.code === 200) {
      const hijriData = data.data.date.hijri;
      return {
        year: parseInt(hijriData.year),
        month: hijriData.month.number,
        day: parseInt(hijriData.day),
        monthName: hijriData.month.ar,
        monthDays: hijriData.month.days,
        weekday: hijriData.weekday.ar,
        holidays: hijriData.holidays || [],
        timings: data.data.timings
      };
    }
  } catch (error) {
    console.warn('Failed to fetch accurate Hijri date:', error);
  }

  // Fallback to approximation if API fails
  return gregorianToHijriFallback(new Date());
}

function gregorianToHijriFallback(gregorianDate) {
  // Islamic calendar started on July 16, 622 CE
  const islamicEpoch = new Date(622, 6, 16);
  const daysSinceEpoch = Math.floor((gregorianDate - islamicEpoch) / (1000 * 60 * 60 * 24));

  // Average Islamic year is about 354.37 days
  const islamicYear = Math.floor(daysSinceEpoch / 354.37) + 1;
  const dayOfYear = Math.floor(daysSinceEpoch % 354.37);

  // Approximate month and day (simplified)
  const islamicMonth = Math.floor(dayOfYear / 29.53) + 1;
  const islamicDay = Math.floor(dayOfYear % 29.53) + 1;

  return {
    year: islamicYear,
    month: Math.min(islamicMonth, 12),
    day: Math.min(islamicDay, 30),
    monthDays: 29 // Fallback
  };
}
  </script>
</body>

</html>